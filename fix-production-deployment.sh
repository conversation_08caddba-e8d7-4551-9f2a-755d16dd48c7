#!/bin/bash

# Production Fix Deployment Script
# This script fixes the approval flow and audit log issues in production

set -e  # Exit on any error

echo "🚀 Starting Production Fix Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

print_status "Checking current directory: $(pwd)"

# 1. Pull latest changes
print_status "Pulling latest changes from repository..."
git pull origin main || {
    print_warning "Git pull failed or no changes to pull"
}

# 2. Install backend dependencies
print_status "Installing backend dependencies..."
npm install

# 3. Build frontend
print_status "Building frontend..."
cd frontend
npm install
npm run build
cd ..

# 4. Check database connection
print_status "Checking database connection..."
NODE_ENV=production npx sequelize-cli db:migrate:status || {
    print_error "Database connection failed. Please check your database credentials."
    exit 1
}

# 5. Run pending migrations
print_status "Running database migrations..."
NODE_ENV=production npx sequelize-cli db:migrate

# 6. Check migration status
print_status "Checking migration status..."
NODE_ENV=production npx sequelize-cli db:migrate:status

# 7. Restart the application
print_status "Restarting application..."

# Check if PM2 is being used
if command -v pm2 &> /dev/null; then
    print_status "Restarting with PM2..."
    pm2 restart digital-user-access-system || pm2 start ecosystem.config.js --env production
elif [ -f "docker-compose.yml" ]; then
    print_status "Restarting with Docker Compose..."
    docker-compose restart
else
    print_warning "No PM2 or Docker found. Please restart your application manually."
fi

# 8. Wait for application to start
print_status "Waiting for application to start..."
sleep 10

# 9. Test the fixes
print_status "Testing the fixes..."

# Test audit logs endpoint
echo "Testing audit logs endpoint..."
curl -s -o /dev/null -w "%{http_code}" "https://digital-us.platcorpgroup.com/user-access/v1/audit-logs" \
    -H "x-subsidiary: platinumtanzania" \
    -H "Authorization: Bearer YOUR_TOKEN_HERE" || print_warning "Could not test audit logs endpoint (authentication required)"

print_status "Deployment completed successfully!"

echo ""
echo "🎉 Production fixes have been deployed!"
echo ""
echo "✅ Fixed Issues:"
echo "   - Database schema mismatch in audit logs model"
echo "   - Added database connection pooling for better performance"
echo "   - Applied all pending migrations"
echo "   - Improved error handling"
echo ""
echo "📋 Next Steps:"
echo "   1. Test the approval flow in your browser"
echo "   2. Check that audit logs load without errors"
echo "   3. Monitor server logs for any remaining issues"
echo ""
echo "🔍 If issues persist:"
echo "   - Check server logs: pm2 logs digital-user-access-system"
echo "   - Verify database connection and credentials"
echo "   - Ensure all migrations completed successfully"
