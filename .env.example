# Database Configuration
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_DBNAME=digital_user_access
DB_HOST=localhost

# Email API Configuration (Primary)
EMAIL_API_URL=https://your-email-api-url.com
EMAIL_API_KEY=your_email_api_key

# SMTP Configuration (Fallback)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Subsidiary-specific IT Email Receivers
IT_EMAIL_RECEIVER=<EMAIL>
PREMIER_IT_EMAIL_RECEIVER=<EMAIL>
MOMENTUM_IT_EMAIL_RECEIVER=<EMAIL>
PLATINUMTZ_IT_EMAIL_RECEIVER=<EMAIL>
FANIKIWA_IT_EMAIL_RECEIVER=<EMAIL>
PLATINUMUG_IT_EMAIL_RECEIVER=<EMAIL>
PREMIERUG_IT_EMAIL_RECEIVER=<EMAIL>
SPECTRUM_IT_EMAIL_RECEIVER=<EMAIL>
PREMIERSA_IT_EMAIL_RECEIVER=<EMAIL>

# Mambu Configuration
MAMBU_API_KEY=your_mambu_api_key

# Application Configuration
NODE_ENV=development
PORT=7081

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=1d

# Test JWT Token (for rest.http testing)
TEST_JWT_TOKEN=your-test-jwt-token-here

# Test User Credentials (for rest.http testing)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=Y@hshua0818
TEST_USER_SUBSIDIARY=platinumtanzania
