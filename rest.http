### loan seaerhc
POST https://platinumkenya.sandbox.mambu.com/api/loans:search?paginationDetails=ON
Content-Type: application/json

{
  "paginationDetails": {
    "limit": 10,
    "offset": 0
  },
  "filterConstraints": [
    {
      "filterSelection": "LOAN_ACCOUNT_ID",
      "filterElement": "EQUALS",
      "value": "8a8186f96f6ec7f3016f7b1f2b7b0b3b"
    }
  ]
}

### Test platinumtanzania branches endpoint
GET https://digital-us.platcorpgroup.com/user-access/v1/access-request/branches
Content-Type: application/json
x-subsidiary: platinumtanzania
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_LOGIN

### Test platinumtanzania roles endpoint
GET https://digital-us.platcorpgroup.com/user-access/v1/access-request/userroles
Content-Type: application/json
x-subsidiary: platinumtanzania
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_LOGIN

### Test platinumtanzania departments endpoint
GET https://digital-us.platcorpgroup.com/user-access/v1/access-request/departments
Content-Type: application/json
x-subsidiary: platinumtanzania
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_LOGIN

### Test platinumkenya branches endpoint (for comparison)
GET https://digital-us.platcorpgroup.com/user-access/v1/access-request/branches
Content-Type: application/json
x-subsidiary: platinumkenya
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_LOGIN

### Test platinumkenya roles endpoint (for comparison)
GET https://digital-us.platcorpgroup.com/user-access/v1/access-request/userroles
Content-Type: application/json
x-subsidiary: platinumkenya
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_LOGIN



### Debug user subsidiary access
GET https://digital-us.platcorpgroup.com/user-access/v1/access-request/debug-user
Content-Type: application/json
x-subsidiary: platinumtanzania
Authorization: Bearer REPLACE_WITH_TOKEN_FROM_LOGIN

### Login to get a fresh token (platinumtanzania user)
POST https://digital-us.platcorpgroup.com/user-access/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Y@hshua0818",
  "subsidiary": "platinumtanzania"
}

### Login to get a fresh token (platinumkenya user for comparison)
POST https://digital-us.platcorpgroup.com/user-access/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin@123",
  "subsidiary": "platinumkenya"
}

