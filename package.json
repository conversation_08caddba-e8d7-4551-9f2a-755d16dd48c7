{"name": "digital-user-access-system", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint --fix src", "server": "nodemon src/index.js", "client": "cd frontend && npm run dev", "clientinstall": "npm start --prefix frontend", "dev": "concurrently \"npm run server\" \"npm run client\"", "start": "node src/index.js", "test": "mocha --exit", "seed": "./scripts/seed-db.sh", "seed:tenant": "npx sequelize-cli db:seed --seed 20240501000000-tenant-seed.js", "seed:users:all": "./scripts/seed-db.sh", "seed:users:platinumkenya": "npx sequelize-cli db:seed --seed 20240501000001-platinumkenya-users.js", "seed:users:premierkenya": "npx sequelize-cli db:seed --seed 20240501000002-premierkenya-users.js", "seed:users:momentumcredit": "npx sequelize-cli db:seed --seed 20240501000003-momentumcredit-users.js", "seed:users:platinumtanzania": "npx sequelize-cli db:seed --seed 20240501000004-platinumtanzania-users.js", "seed:users:premierfanikiwa": "npx sequelize-cli db:seed --seed 20240501000005-premierfanikiwa-users.js", "seed:users:platinumuganda": "npx sequelize-cli db:seed --seed 20240501000006-platinumuganda-users.js", "seed:users:premieruganda": "npx sequelize-cli db:seed --seed 20240501000007-premieruganda-users.js", "seed:users:spectrumzambia": "npx sequelize-cli db:seed --seed 20240501000008-spectrumzambia-users.js", "seed:users:premiersouthafrica": "npx sequelize-cli db:seed --seed 20240501000009-premiersouthafrica-users.js"}, "dependencies": {"@fast-csv/format": "^5.0.2", "@headlessui/vue": "^1.7.23", "@sendgrid/mail": "^8.1.4", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-mocha": "^5.0.8", "@vueform/multiselect": "^2.6.11", "autoprefixer": "^10.4.20", "axios": "^0.26.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "bull": "^4.7.0", "chart.js": "^3.9.1", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "^16.4.7", "express": "~4.16.1", "fast-csv": "^5.0.2", "http-errors": "~1.6.3", "jade": "~1.11.0", "jsonwebtoken": "^9.0.2", "morgan": "~1.9.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "papaparse": "^5.5.2", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "pinia": "^2.3.1", "postcss": "^8.5.1", "sequelize": "^6.37.5", "signature_pad": "^5.0.4", "tailwindcss": "^4.0.3", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-chartjs": "^4.1.2", "vue-cli-plugin-vuetify": "^2.5.8", "vue-multiselect": "^3.2.0", "vue-router": "^3.6.5", "vue-signature-pad": "^3.0.2", "vuetify": "^3.7.12", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"chai": "^5.2.0", "chai-http": "^5.1.1", "concurrently": "^7.0.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.32.0", "mocha": "^11.1.0", "nodemon": "^2.0.15", "prettier": "3.4.2", "sequelize-cli": "^6.6.2"}, "imports": {"#src/*": "./src/*.js", "#api/*": "./src/api/*.js", "#controllers/*": "./src/controllers/*.js", "#routes/*": "./src/routes/*.js", "#services/*": "./src/services/*.js", "#utils/*": "./src/utils/*.js", "#jobs/*": "./src/jobs/*.js", "#db/*": "./src/db/*.js", "#models/*": "./src/db/models/*.js", "#middlewares/*": "./src/_middlewares/*.js"}}