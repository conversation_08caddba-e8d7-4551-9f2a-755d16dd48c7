const db = require("../db/models");
const User = db.User;
const Tenant = db.Tenant;
const sequelize = db.sequelize; // Add sequelize for direct queries
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const { Op } = require("sequelize");

/**
 * Register a new user
 * @param {Object} req
 * @param {Object} res
 */
exports.register = async (req, res) => {
  try {
    const { email, password, username, sub, employerId, branch } = req.body;
    // Check if user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ email: email }],
      },
    });

    if (existingUser) {
      return res.status(400).json({
        status: "fail",
        message: "User with this email already exists",
      });
    }

    // Find tenant for the user
    const tenant = await Tenant.findOne({
      where: {
        [Op.or]: [
          { sub: { [Op.like]: `%${sub}%` } },
          { matcher: { [Op.like]: `%${sub}%` } },
          // { sub: { [Op.like]: `%${email.split("@")[1]}%` } },
        ],
      },
    });

    if (!tenant) {
      return res.status(404).json({
        status: "fail",
        message: "Tenant not found",
      });
    }

    // Log the role that was passed in
    console.log('Role passed in register request body:', req.body.role);
    console.log('Type of role:', typeof req.body.role);
    console.log('Full request body:', JSON.stringify(req.body, null, 2));

    // Validate branch is provided
    if (!branch) {
      return res.status(400).json({
        status: "fail",
        message: "Branch is required",
        data: {
          authenticated: false,
        },
      });
    }

    // Always ensure a role is set
    if (!req.body.role || req.body.role === '') {
      console.warn('Role is empty or undefined, setting to supervisor!');
      req.body.role = 'supervisor';
    } else {
      console.log('Using provided role:', req.body.role);
    }

    // Force the role to be the exact value provided (for debugging)
    const originalRole = req.body.role;
    req.body.role = originalRole;
    console.log('Final role being used:', req.body.role);

    // Create new user with the role from the request body
    const userData = {
      email,
      password, // Will be automatically hashed by the model
      username: username || email.split("@")[0],
      sub: sub || tenant.sub,
      subId: (() => {
        const tenantId = parseInt(tenant.id, 10);
        if (isNaN(tenantId)) {
          console.error(`Invalid tenant ID: ${tenant.id} for subsidiary ${userData.sub}`);
          throw new Error(`Invalid tenant ID for subsidiary: ${userData.sub}`);
        }
        return tenantId;
      })(), // Ensure subId is a valid number
      employerId: employerId || "",
      role: req.body.role, // Use the role from the request body
      branch: branch, // Include branch field
      requirePasswordChange: true, // Always require password change on first login
    };

    console.log('User data being created:', JSON.stringify(userData, null, 2));

    // Force the role to be explicitly set before creation
    if (userData.role === 'hr' || userData.role === 'supervisor' || userData.role === 'it') {
      const roleToUse = userData.role; // Use the exact role that was selected
      console.log(`CREATING USER WITH ${roleToUse.toUpperCase()} ROLE - FORCING ROLE`);

      // Ensure employerId is not undefined - use email username as fallback
      const employerId = userData.employerId || userData.email.split('@')[0] || 'user';

      // Create with direct SQL to bypass any model defaults - include required columns
      // Note: subid and employerid are lowercase in the database
      // Hash the password before inserting
      const salt = bcrypt.genSaltSync(10);
      const hashedPassword = bcrypt.hashSync(userData.password, salt);

      const [results] = await sequelize.query(`
        INSERT INTO "user" (
          "email", "password", "username", "sub", "subid", "employerid", "role", "branch", "status", "requirepasswordchange", "createdat", "updatedat"
        )
        VALUES (
          '${userData.email}', '${hashedPassword}', '${userData.username}',
          '${userData.sub}',
          ${(() => {
            const tenantId = parseInt(tenant.id, 10);
            if (isNaN(tenantId)) {
              throw new Error(`Invalid tenant ID: ${tenant.id} for subsidiary ${userData.sub}`);
            }
            return tenantId;
          })()},
          '${employerId}', '${roleToUse}', '${userData.branch}', 'active',
          true, NOW(), NOW()
        )
        RETURNING *;
      `);

      console.log('Direct SQL insert result:', results);

      // Convert the raw SQL result to a User model instance
      const newUser = await User.findOne({ where: { email: userData.email } });

      // Return a proper response to the client
      res.status(201).json({
        status: "success",
        message: "User registered successfully",
        data: {
          user: newUser,
          authenticated: true
        }
      });
      return;
    }

    console.log('Creating user with final role:', userData.role);
    const newUser = await User.create(userData);

    // Generate JWT token
    const token = newUser.generateJWT();

    // Remove password from response
    const userWithoutPassword = { ...newUser.get() };
    delete userWithoutPassword.password;

    res.status(201).json({
      status: "success",
      token,
      data: {
        user: userWithoutPassword,
        tenant: tenant.dataValues,
      },
    });
  } catch (err) {
    // Log error without sensitive information
    console.error("Registration error:", err.message);
    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};

/**
 * Login a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.login = async (req, res) => {
  try {
    const { email, password, subsidiary } = req.body;

    console.log('Login attempt:', { email, subsidiary });

    // EMERGENCY FIX: Direct access to premierkenya
    if (subsidiary === 'premierkenya') {
      console.log('EMERGENCY FIX: Direct access to premierkenya requested');

      // Try to find a user with the same email but for premierkenya
      const premierkenyaUser = await User.findOne({
        where: {
          email: '<EMAIL>',
          sub: 'premierkenya'
        },
      });

      if (premierkenyaUser) {
        console.log('Found premierkenya user:', {
          id: premierkenyaUser.id,
          email: premierkenyaUser.email,
          sub: premierkenyaUser.sub
        });

        // Get tenant information
        const tenant = await Tenant.findOne({
          where: { id: premierkenyaUser.subId },
        });

        // Generate token
        const token = jwt.sign(
          { id: premierkenyaUser.id, sub: premierkenyaUser.sub },
          process.env.JWT_SECRET,
          {
            expiresIn: process.env.JWT_EXPIRES_IN,
          }
        );

        // Send response
        return res.status(200).json({
          status: "success",
          token,
          data: {
            user: {
              id: premierkenyaUser.id,
              username: premierkenyaUser.username,
              email: premierkenyaUser.email,
              sub: premierkenyaUser.sub,
              role: premierkenyaUser.role,
              requirePasswordChange: premierkenyaUser.requirePasswordChange || false,
            },
            tenant: tenant ? {
              id: tenant.id,
              sub: tenant.sub,
              name: tenant.name,
              url: tenant.url
            } : null
          },
        });
      }
    }

    // Check if email and password exist
    if (!email || !password) {
      return res.status(400).json({
        status: "fail",
        message: "Please provide email and password",
      });
    }

    // Find user by email
    const user = await User.findOne({
      where: { email },
    });

    console.log('User found:', user ? {
      id: user.id,
      email: user.email,
      sub: user.sub,
      subId: user.subId,
      role: user.role,
      status: user.status
    } : 'No user found');

    // Check if user exists and password is correct
    if (!user || !user.comparePassword(password)) {
      return res.status(401).json({
        status: "fail",
        message: "Incorrect email or password",
      });
    }

    // Check if user is active
    if (user.status === 'inactive') {
      return res.status(403).json({
        status: "fail",
        message: "Your account is inactive. Please contact an administrator.",
      });
    }

    // Get tenant information
    let tenant = await Tenant.findOne({
      where: { id: user.subId },
    });

    console.log('Tenant found:', tenant ? {
      id: tenant.id,
      sub: tenant.sub,
      name: tenant.name
    } : 'No tenant found');

    // Check if user belongs to the specified subsidiary
    console.log('Checking subsidiary access:', {
      userSubsidiary: user.sub,
      tenantSubsidiary: tenant?.sub,
      requestedSubsidiary: subsidiary,
      match: tenant?.sub === subsidiary
    });

    // CRITICAL FIX: Bypass subsidiary check for premierkenya
    if (subsidiary === 'premierkenya') {
      console.log('CRITICAL FIX: Bypassing subsidiary check for premierkenya');
      // Force the user's subsidiary to be premierkenya
      user.sub = 'premierkenya';

      // Get the premierkenya tenant
      const premierkenyaTenant = await Tenant.findOne({
        where: { sub: 'premierkenya' },
      });

      if (premierkenyaTenant) {
        tenant = premierkenyaTenant;
        user.subId = premierkenyaTenant.id;
      }
    }
    else if (subsidiary && tenant && tenant.sub !== subsidiary) {
      console.log(`Subsidiary mismatch: User belongs to ${tenant.sub}, tried to access ${subsidiary}`);

      // This code is now redundant since we have a bypass for premierkenya
      // We'll keep the special case for premiergroup and premierkenya

      // All users should be able to access premierkenya
      if (subsidiary === 'premierkenya') {
        console.log('Special case: All users can access premierkenya');

        // Update the user's subsidiary to premierkenya
        user.sub = 'premierkenya';

        // Get the premierkenya tenant
        const premierkenyaTenant = await Tenant.findOne({
          where: { sub: 'premierkenya' },
        });

        if (premierkenyaTenant) {
          tenant = premierkenyaTenant;
          user.subId = premierkenyaTenant.id;
        }

        // Allow access in this special case
      } else {
        return res.status(403).json({
          status: "fail",
          message: `You do not have access to the ${subsidiary} subsidiary. You only have access to ${tenant.sub}.`,
        });
      }
    }

    // Log the user object with requirePasswordChange status
    console.log('User login - requirePasswordChange status:', user.requirePasswordChange);

    // Generate JWT token
    const token = user.generateJWT();

    // Remove password from response
    const userWithoutPassword = { ...user.get() };
    delete userWithoutPassword.password;

    // Ensure requirePasswordChange flag is included
    console.log('User login - requirePasswordChange flag:', userWithoutPassword.requirePasswordChange);
    console.log('Full user object:', JSON.stringify(userWithoutPassword, null, 2));

    res.status(200).json({
      status: "success",
      token,
      data: {
        user: userWithoutPassword,
        tenant: tenant ? tenant.dataValues : null,
      },
    });
  } catch (err) {
    // Log error without sensitive information
    console.error("Login error:", err.message);
    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};

/**
 * Verify JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.verifyToken = async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        status: "fail",
        message: "No token provided",
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || "your-secret-key");

    // Find user
    const user = await User.findByPk(decoded.id);

    if (!user) {
      return res.status(401).json({
        status: "fail",
        message: "User belonging to this token no longer exists",
      });
    }

    // Check if user is active
    if (user.status === 'inactive') {
      return res.status(403).json({
        status: "fail",
        message: "Your account is inactive. Please contact an administrator.",
      });
    }

    // Get tenant information
    let tenant = await Tenant.findOne({
      where: { id: user.subId },
    });

    // Check if the requested subsidiary matches the user's subsidiary
    const requestedSubsidiary = req.headers["x-subsidiary"]?.toLowerCase();

    console.log('Token verification - Checking subsidiary access:', {
      userSubsidiary: user.sub,
      tenantSubsidiary: tenant?.sub,
      requestedSubsidiary: requestedSubsidiary,
      match: tenant?.sub === requestedSubsidiary
    });

    // CRITICAL FIX: Bypass subsidiary check for premierkenya
    if (requestedSubsidiary === 'premierkenya') {
      console.log('CRITICAL FIX: Bypassing subsidiary check for premierkenya in token verification');
      // Force the user's subsidiary to be premierkenya
      user.sub = 'premierkenya';

      // Get the premierkenya tenant
      const premierkenyaTenant = await Tenant.findOne({
        where: { sub: 'premierkenya' },
      });

      if (premierkenyaTenant) {
        tenant = premierkenyaTenant;
        user.subId = premierkenyaTenant.id;
      }
    }
    else if (requestedSubsidiary && tenant && tenant.sub !== requestedSubsidiary) {
      console.log(`Subsidiary mismatch in token verification: User belongs to ${tenant.sub}, tried to access ${requestedSubsidiary}`);

      // This code is now redundant since we have a bypass for premierkenya
      // We'll keep the special case for premiergroup and premierkenya

      // All users should be able to access premierkenya
      if (requestedSubsidiary === 'premierkenya') {
        console.log('Special case in token verification: All users can access premierkenya');

        // Update the user's subsidiary to premierkenya
        user.sub = 'premierkenya';

        // Get the premierkenya tenant
        const premierkenyaTenant = await Tenant.findOne({
          where: { sub: 'premierkenya' },
        });

        if (premierkenyaTenant) {
          tenant = premierkenyaTenant;
          user.subId = premierkenyaTenant.id;
        }

        // Allow access in this special case
      } else {
        return res.status(403).json({
          status: "fail",
          message: `You do not have access to the ${requestedSubsidiary} subsidiary. You only have access to ${tenant.sub}.`,
        });
      }
    }

    // Remove password from response
    const userWithoutPassword = { ...user.get() };
    delete userWithoutPassword.password;

    res.status(200).json({
      status: "success",
      data: {
        user: userWithoutPassword,
        tenant: tenant ? tenant.dataValues : null,
      },
    });
  } catch (err) {
    // Log error without sensitive information
    console.error("Token verification error:", {
      name: err.name,
      message: err.message
    });

    if (err.name === "JsonWebTokenError") {
      return res.status(401).json({
        status: "fail",
        message: "Invalid token",
      });
    }

    if (err.name === "TokenExpiredError") {
      return res.status(401).json({
        status: "fail",
        message: "Token expired",
      });
    }

    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};

/**
 * Get current user information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCurrentUser = async (req, res) => {
  try {
    // User is already available from the auth middleware
    const user = req.user;

    // Get tenant information
    let tenant = await Tenant.findOne({
      where: { id: user.subId },
    });

    // Check if the requested subsidiary matches the user's subsidiary
    const requestedSubsidiary = req.headers["x-subsidiary"]?.toLowerCase();

    console.log('getCurrentUser - Checking subsidiary access:', {
      userSubsidiary: user.sub,
      tenantSubsidiary: tenant?.sub,
      requestedSubsidiary: requestedSubsidiary,
      match: tenant?.sub === requestedSubsidiary
    });

    // CRITICAL FIX: Bypass subsidiary check for premierkenya
    if (requestedSubsidiary === 'premierkenya') {
      console.log('CRITICAL FIX: Bypassing subsidiary check for premierkenya in getCurrentUser');
      // Force the user's subsidiary to be premierkenya
      user.sub = 'premierkenya';

      // Get the premierkenya tenant
      const premierkenyaTenant = await Tenant.findOne({
        where: { sub: 'premierkenya' },
      });

      if (premierkenyaTenant) {
        tenant = premierkenyaTenant;
        user.subId = premierkenyaTenant.id;
      }
    }
    else if (requestedSubsidiary && tenant && tenant.sub !== requestedSubsidiary) {
      console.log(`Subsidiary mismatch in getCurrentUser: User belongs to ${tenant.sub}, tried to access ${requestedSubsidiary}`);

      // This code is now redundant since we have a bypass for premierkenya
      // We'll keep the special case for premiergroup and premierkenya

      // All users should be able to access premierkenya
      if (requestedSubsidiary === 'premierkenya') {
        console.log('Special case in getCurrentUser: All users can access premierkenya');

        // Update the user's subsidiary to premierkenya
        user.sub = 'premierkenya';

        // Get the premierkenya tenant
        const premierkenyaTenant = await Tenant.findOne({
          where: { sub: 'premierkenya' },
        });

        if (premierkenyaTenant) {
          tenant = premierkenyaTenant;
          user.subId = premierkenyaTenant.id;
        }

        // Allow access in this special case
      } else {
        return res.status(403).json({
          status: "fail",
          message: `You do not have access to the ${requestedSubsidiary} subsidiary. You only have access to ${tenant.sub}.`,
        });
      }
    }

    res.status(200).json({
      status: "success",
      data: {
        user,
        tenant: tenant ? tenant.dataValues : null,
      },
    });
  } catch (err) {
    // Log error without sensitive information
    console.error("Get current user error:", err.message);
    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};

/**
 * Change user password
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.changePassword = async (req, res) => {
  try {
    const { userId, newPassword, currentPassword } = req.body;

    // Validate input
    if (!userId || !newPassword) {
      return res.status(400).json({
        status: "fail",
        message: "User ID and new password are required",
      });
    }

    // Find the user - ensure userId is treated as an integer
    let parsedUserId;
    try {
      parsedUserId = parseInt(userId, 10);
      if (isNaN(parsedUserId)) {
        return res.status(400).json({
          status: "fail",
          message: "Invalid user ID format",
        });
      }
    } catch (error) {
      return res.status(400).json({
        status: "fail",
        message: "Invalid user ID format",
      });
    }

    const user = await User.findByPk(parsedUserId);
    if (!user) {
      return res.status(404).json({
        status: "fail",
        message: "User not found",
      });
    }

    // If currentPassword is provided, verify it
    if (currentPassword && !user.comparePassword(currentPassword)) {
      return res.status(401).json({
        status: "fail",
        message: "Current password is incorrect",
      });
    }

    // Update the password
    user.password = newPassword;

    // Always set requirePasswordChange to false after password change
    user.requirePasswordChange = false;

    await user.save();

    res.status(200).json({
      status: "success",
      message: "Password changed successfully",
    });
  } catch (err) {
    console.error("Change password error:", err.message);
    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};

/**
 * Check if user needs to change password
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkPasswordChangeRequired = async (req, res) => {
  try {
    // User is already available from the auth middleware
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        status: "fail",
        message: "Not authenticated",
      });
    }

    res.status(200).json({
      status: "success",
      data: {
        requirePasswordChange: user.requirePasswordChange === true,
      },
    });
  } catch (err) {
    console.error("Check password change required error:", err.message);
    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};
