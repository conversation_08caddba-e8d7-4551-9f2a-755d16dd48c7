const { Op } = require("sequelize");
const fs = require("fs");
const { format } = require("@fast-csv/format");
const { UserAccessRequest: AccessForm,} = require("../db/models");

const db = require("../db/models");

const Approval = db.Approval;
const AuditLog = db.AuditLog;

// Get Access Form History Report
const getAccessFormHistoryReport = async (req, res) => {
  try {
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();

    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50; // Default to 50 records per page
    const offset = (page - 1) * limit;

    console.log(`Access Form History Report - Subsidiary: ${subsidiary}, Page: ${page}, Limit: ${limit}`);

    const result = await AccessForm.findAndCountAll({
      where: { subId: subsidiary },
      order: [["createdAt", "DESC"]],
      limit,
      offset
    });

    console.log(`Successfully fetched ${result.rows.length} access forms (page ${page} of ${Math.ceil(result.count / limit)}) for subsidiary: ${subsidiary}`);

    res.status(200).json({
      data: result.rows,
      pagination: {
        page,
        limit,
        total: result.count,
        totalPages: Math.ceil(result.count / limit),
        hasNext: page < Math.ceil(result.count / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error("Error fetching access form history:", error);
    console.error("Error details:", error.message);
    console.error("Error stack:", error.stack);
    res.status(500).json({ error: "Failed to generate report" });
  }
};

// Get Audit Log Report
const getAuditLogReport = async (req, res) => {
  try {
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();

    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    console.log(`Audit Log Report - Subsidiary: ${subsidiary}, Page: ${page}, Limit: ${limit}`);

    const result = await AuditLog.findAndCountAll({
      where: { subId: subsidiary },
      order: [["createdAt", "DESC"]],
      limit,
      offset
    });

    console.log(`Successfully fetched ${result.rows.length} audit logs (page ${page} of ${Math.ceil(result.count / limit)}) for subsidiary: ${subsidiary}`);

    res.status(200).json({
      data: result.rows,
      pagination: {
        page,
        limit,
        total: result.count,
        totalPages: Math.ceil(result.count / limit),
        hasNext: page < Math.ceil(result.count / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    console.error("Error details:", error.message);
    res.status(500).json({ error: "Failed to generate report" });
  }
};

// Get Pending Approval Report
const getPendingApprovalReport = async (req, res) => {
  try {
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();

    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    console.log(`Pending Approval Report - Subsidiary: ${subsidiary}, Page: ${page}, Limit: ${limit}`);

    const result = await Approval.findAndCountAll({
      where: { status: "Pending", subId: subsidiary },
      order: [["createdAt", "DESC"]],
      limit,
      offset
    });

    console.log(`Successfully fetched ${result.rows.length} pending approvals (page ${page} of ${Math.ceil(result.count / limit)}) for subsidiary: ${subsidiary}`);

    res.status(200).json({
      data: result.rows,
      pagination: {
        page,
        limit,
        total: result.count,
        totalPages: Math.ceil(result.count / limit),
        hasNext: page < Math.ceil(result.count / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error("Error fetching pending approvals:", error);
    console.error("Error details:", error.message);
    res.status(500).json({ error: "Failed to generate report" });
  }
};

// Export Report as CSV using fast-csv
const exportReportAsCSV = async (req, res) => {
  try {
    const { reportType } = req.params;
    const subsidiary = req.query.subId?.toLowerCase();
    let data = [];

    switch (reportType) {
      case "access":
        data = await AccessForm.findAll({
          where: { subId: subsidiary },
          raw: true,
        });
        break;
      case "audit":
        data = await AuditLog.findAll({
          where: { subId: subsidiary },
          raw: true,
        });
        break;
      case "approvals":
        data = await Approval.findAll({
          where: { status: "Pending", subId: subsidiary },
          raw: true,
        });
        break;
      case "revocation":
        data = await RevocationForm.findAll({
          where: { subId: subsidiary },
          raw: true,
        });
        break;
      default:
        return res.status(400).json({ error: "Invalid report type" });
    }

    const csvStream = format({ headers: true });
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${reportType}_report.csv`
    );

    csvStream.pipe(res);
    data.forEach((row) => csvStream.write(row));
    csvStream.end();
  } catch (error) {
    console.error("Error exporting report:", error);
    res.status(500).json({ error: "Failed to export report" });
  }
};

module.exports = {
  getAccessFormHistoryReport,
  getAuditLogReport,
  getPendingApprovalReport,
  exportReportAsCSV,
};
