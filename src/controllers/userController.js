const { Op } = require("sequelize");
const db = require("../db/models");
const User = db.User;
const Tenant = db.Tenant;
const sequelize = db.sequelize; // Add sequelize for direct queries
const bcrypt = require("bcryptjs");

const { extractMambuUser } = require("../utils/extractMambuUser");
const { UserService } = require("../services/userService");
// Get all users
exports.getAllUsers = async (req, res) => {
  try {
    // Get subsidiary from header or from authenticated user
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || req.user.sub;

    // Prepare query conditions
    let whereCondition = {};

    // If user is not systemAdmin, scope to their subsidiary
    if (req.user.role !== 'systemAdmin') {
      whereCondition.sub = subsidiary;
    } else if (subsidiary && subsidiary !== 'all') {
      // If systemAdmin and a specific subsidiary is requested
      whereCondition.sub = subsidiary;
    }

    // Find users based on conditions
    const users = await User.findAll({
      where: whereCondition,
      attributes: { exclude: ['password'] }, // Exclude password from results
      order: [['createdAt', 'DESC']] // Sort by creation date, newest first
    });

    res.status(200).json({
      status: "success",
      results: users.length,
      data: { users },
    });
  } catch (err) {
    console.error("Error fetching users:", err);
    res.status(500).json({
      status: "error",
      message: err.message,
    });
  }
};

// Get a single user
exports.getUser = async (req, res) => {
  try {
    // Get subsidiary from header or from authenticated user
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || req.user.sub;

    // Prepare query conditions
    let whereCondition = { id: req.params.id };

    // If user is not systemAdmin, scope to their subsidiary
    if (req.user.role !== 'systemAdmin') {
      whereCondition.sub = subsidiary;
    }

    // Find user by ID and scoped appropriately
    const user = await User.findOne({
      where: whereCondition,
      attributes: { exclude: ['password'] } // Exclude password from results
    });

    if (!user) {
      return res.status(404).json({
        status: "fail",
        message: "User not found",
      });
    }

    res.status(200).json({
      status: "success",
      data: { user },
    });
  } catch (err) {
    console.error("Error fetching user:", err);
    res.status(500).json({
      status: "error",
      message: err.message,
    });
  }
};

// Create a new user
exports.createUser = async (req, res) => {
  // Remove sensitive logging
  let mambuUser = req.header("mambuUser");

  // Only log in development environment
  if (process.env.NODE_ENV === 'development') {
    console.log("Request headers:", {
      subsidiary: req.headers["x-subsidiary"],
      contentType: req.headers["content-type"],
      hasMambuUser: !!mambuUser
    });
  }

  // Skip Mambu user extraction for bulk uploads and direct API calls
  // This prevents errors when mambuUser is undefined
  if (req.path === '/auth/register' || req.originalUrl.includes('/auth/register')) {
    console.log('Skipping Mambu user extraction for auth/register endpoint');
  }
  else if (mambuUser !== "null" && mambuUser !== null && mambuUser !== undefined) {
    try {
      mambuUser = extractMambuUser(mambuUser);
      console.log(mambuUser, "mambuUser");

      req.body.sub = mambuUser.sub;
      try {
        const userDetails = await new UserService({
          baseUrl: mambuUser.base_url,
          apiKey: mambuUser.apiKey,
        }).getUser(mambuUser.userId);
        if (userDetails) {
          req.body.username = userDetails.username || userDetails.id;
          req.body.eid = userDetails.id;
          req.body.email = userDetails.email;
          // req.body.sub = mambuUser.domain.split("://")[1].split(".")[0];
          console.log(req.body, "userDetails");
        }
      } catch (error) {
        console.warn("Failed to fetch user details from Mambu:", error.message);
      }
    } catch (error) {
      console.warn("Failed to extract Mambu user information:", error.message);
      // Continue with the request even if Mambu user extraction fails
    }
  }

  const { email, sub, eid } = req.body;
  try {
    // Check if the user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ email: email }],
      },
    });

    if (existingUser) {
      // get user tenant id - ensure subId is treated as an integer
      let tenantId;
      try {
        tenantId = parseInt(existingUser.dataValues.subId, 10);
        if (isNaN(tenantId)) {
          // If subId is not a valid integer, try to find tenant by subsidiary name
          const tenant = await Tenant.findOne({
            where: {
              sub: existingUser.dataValues.sub
            },
          });
          return res.status(200).json({
            status: "success",
            data: {
              authenticated: true,
              user: existingUser.dataValues,
              tenant: tenant ? tenant.dataValues : null,
            },
          });
        }
      } catch (error) {
        console.error('Error parsing tenant ID:', error);
        // If there's an error, try to find tenant by subsidiary name
        const tenant = await Tenant.findOne({
          where: {
            sub: existingUser.dataValues.sub
          },
        });
        return res.status(200).json({
          status: "success",
          data: {
            authenticated: true,
            user: existingUser.dataValues,
            tenant: tenant ? tenant.dataValues : null,
          },
        });
      }

      // Find tenant by ID
      const tenant = await Tenant.findOne({
        where: { id: tenantId },
      });

      // return authenticated user and tenant
      return res.status(200).json({
        status: "success",
        data: {
          authenticated: true,
          user: existingUser.dataValues,
          tenant: tenant.dataValues,
        },
      });
    }

    // find tenat user id sub
    const tenant = await Tenant.findOne({
      where: {
        [Op.or]: [
          { sub: { [Op.like]: `%${sub}%` } },
          { matcher: { [Op.like]: `%${sub}%` } },
          { sub: { [Op.like]: `%${email.split("@")[-1]}%` } },
        ],
      },
    });

    if (!tenant) {
      return res.status(404).json({
        status: "fail",
        data: {
          authenticated: false,
        },
        message: "Tenant not found",
      });
    }

    // Create a new user
    const tenantId = parseInt(tenant.dataValues.id, 10);

    // Ensure subId is a valid number
    if (isNaN(tenantId)) {
      console.error(`Invalid tenant ID: ${tenant.dataValues.id} for subsidiary ${req.body.sub}`);
      return res.status(400).json({
        status: "fail",
        message: `Invalid tenant ID for subsidiary: ${req.body.sub}`
      });
    }

    req.body.subId = tenantId; // Assign the validated tenant ID
    req.body.employerId = eid || email.split("@")[0]; // Fallback to email username if eid is undefined
    req.body.username = email.split("@")[0];

    console.log(`Using tenant ID ${req.body.subId} (${typeof req.body.subId}) for subsidiary ${req.body.sub}`);

    // Set role if provided, otherwise default to 'supervisor'
    // Log the role that was passed in
    console.log('Role passed in request body:', req.body.role);

    // Always ensure a role is set
    if (!req.body.role) {
      req.body.role = 'supervisor';
      console.log('No role provided, defaulting to supervisor');
    } else {
      console.log('Using provided role:', req.body.role);
    }

    // Force the role to be the exact value provided (for debugging)
    const originalRole = req.body.role;
    req.body.role = originalRole;
    console.log('Final role being used:', req.body.role);

    // Validate role is one of the allowed values
    const allowedRoles = ['systemAdmin', 'hr', 'it', 'supervisor', 'loanadmin'];
    if (!allowedRoles.includes(req.body.role)) {
      return res.status(400).json({
        status: "fail",
        message: "Invalid role. Must be one of: systemAdmin, hr, it, supervisor, loanadmin",
        data: {
          authenticated: false,
        },
      });
    }

    // Set status if provided, otherwise default to 'active'
    req.body.status = req.body.status || 'active';

    // Validate status is one of the allowed values
    const allowedStatuses = ['active', 'inactive'];
    if (!allowedStatuses.includes(req.body.status)) {
      return res.status(400).json({
        status: "fail",
        message: "Invalid status. Must be one of: active, inactive",
        data: {
          authenticated: false,
        },
      });
    }

    // Validate branch is provided
    if (!req.body.branch) {
      return res.status(400).json({
        status: "fail",
        message: "Branch is required",
        data: {
          authenticated: false,
        },
      });
    }

    // Always set requirePasswordChange to true for new users
    req.body.requirePasswordChange = true;
    console.log('Setting requirePasswordChange to true for new user:', req.body.email);

    // Create a clean object with only the fields we need
    const userData = {
      email: req.body.email,
      password: req.body.password,
      username: req.body.username,
      sub: req.body.sub,
      subId: parseInt(req.body.subId, 10), // Ensure subId is a number
      employerId: req.body.employerId,
      // Explicitly set the role - this is the key fix
      role: req.body.role,
      status: req.body.status || 'active',
      branch: req.body.branch, // Branch is required
      requirePasswordChange: true
    };

    console.log('Creating user with explicit role:', userData.role);
    console.log('Branch value being sent:', userData.branch);
    console.log('Final user data being sent to create:', JSON.stringify(userData, null, 2));

    // Special handling for specific roles to bypass any model defaults
    if (userData.role === 'hr' || userData.role === 'supervisor' || userData.role === 'it') {
      const roleToUse = userData.role; // Use the exact role that was selected
      console.log(`CREATING USER WITH ${roleToUse.toUpperCase()} ROLE - FORCING ROLE`);

      try {
        // Ensure employerId is not undefined - use email username as fallback
        const employerId = userData.employerId || userData.email.split('@')[0] || 'user';

        // Create with direct SQL to bypass any model defaults - include required columns
        // Note: subid and employerid are lowercase in the database
        // Hash the password before inserting
        const salt = bcrypt.genSaltSync(10);
        const hashedPassword = bcrypt.hashSync(userData.password, salt);

        // Find the tenant ID
        const tenant = await Tenant.findOne({
          where: {
            [Op.or]: [
              { sub: { [Op.like]: `%${userData.sub}%` } },
              { matcher: { [Op.like]: `%${userData.sub}%` } },
            ],
          },
        });

        if (!tenant) {
          throw new Error(`Tenant not found for subsidiary: ${userData.sub}`);
        }

        console.log(`Found tenant for ${userData.sub}:`, tenant.id);

        const [results] = await sequelize.query(`
          INSERT INTO "user" (
            "email", "password", "username", "sub", "subid", "employerid", "role", "status", "branch", "requirepasswordchange", "createdat", "updatedat"
          )
          VALUES (
            '${userData.email}', '${hashedPassword}', '${userData.username}',
            '${userData.sub}',
            ${(() => {
              const tenantId = parseInt(tenant.id, 10);
              if (isNaN(tenantId)) {
                throw new Error(`Invalid tenant ID: ${tenant.id} for subsidiary ${userData.sub}`);
              }
              return tenantId;
            })()},
            '${employerId}', '${roleToUse}', '${userData.status}',
            ${userData.branch ? `'${userData.branch}'` : 'NULL'},
            ${userData.requirePasswordChange === false ? 'false' : 'true'}, NOW(), NOW()
          )
          RETURNING *;
        `);

        console.log('Direct SQL insert result:', results);

        // Check if we got a result
        if (!results || results.length === 0) {
          throw new Error('Failed to create user with direct SQL');
        }
      } catch (sqlError) {
        console.error('SQL Error creating user:', sqlError);
        console.error('SQL Error details:', JSON.stringify(sqlError, null, 2));
        throw new Error(`Failed to create user: ${sqlError.message}`);
      }

      // Convert the raw SQL result to a User model instance
      const newUser = await User.findOne({ where: { email: userData.email } });

      // Return a proper response to the client
      res.status(201).json({
        status: "success",
        message: "User created successfully",
        data: {
          user: newUser,
          authenticated: true
        }
      });
      return;
    }

    // For other roles, use the standard create method
    const newUser = await User.create(userData);

    // Add audit log entry
    if (req.auditData) {
      req.auditData.userName = newUser.username;
      req.auditData.formId = newUser.id;
    }

    // Remove password from response
    const userWithoutPassword = { ...newUser.get() };
    delete userWithoutPassword.password;

    res.status(201).json({
      status: "success",
      data: { user: userWithoutPassword, tenant: tenant.dataValues, authenticated: true },
    });
  } catch (err) {
    console.error('Error creating user:', err);
    console.error('Error details:', JSON.stringify(err, null, 2));

    // If it's a Sequelize validation error, log the specific validation errors
    if (err.name === 'SequelizeValidationError') {
      console.error('Validation errors:', err.errors.map(e => e.message));
    }

    res.status(400).json({
      status: "fail",
      message: err.message,
      data: {
        authenticated: false,
        error: err.name,
        details: err.errors ? err.errors.map(e => e.message) : null
      },
    });
  }
};

// Update a user
exports.updateUser = async (req, res) => {
  try {
    // Get subsidiary from header or from authenticated user
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || req.user.sub;

    // Prepare query conditions
    let whereCondition = { id: req.params.id };

    // If user is not systemAdmin, scope to their subsidiary
    if (req.user.role !== 'systemAdmin') {
      whereCondition.sub = subsidiary;
    }

    // Find user by ID and scoped appropriately
    const user = await User.findOne({
      where: whereCondition
    });

    if (!user) {
      return res.status(404).json({
        status: "fail",
        message: "User not found",
      });
    }

    // Validate role if provided
    if (req.body.role) {
      const allowedRoles = ['systemAdmin', 'hr', 'it', 'supervisor', 'loanadmin'];
      if (!allowedRoles.includes(req.body.role)) {
        return res.status(400).json({
          status: "fail",
          message: "Invalid role. Must be one of: systemAdmin, hr, it, supervisor, loanadmin",
        });
      }
    }

    // Validate status if provided
    if (req.body.status) {
      const allowedStatuses = ['active', 'inactive'];
      if (!allowedStatuses.includes(req.body.status)) {
        return res.status(400).json({
          status: "fail",
          message: "Invalid status. Must be one of: active, inactive",
        });
      }
    }

    // Update user fields
    const updatableFields = ['username', 'email', 'role', 'status', 'branch'];
    updatableFields.forEach(field => {
      if (req.body[field] !== undefined) {
        console.log(`Updating user ${user.id} field '${field}' from '${user[field]}' to '${req.body[field]}'`);
        user[field] = req.body[field];
      }
    });

    // Update password if provided
    if (req.body.password) {
      // If currentPassword is provided, verify it before updating
      if (req.body.currentPassword) {
        // This is a self-password reset by a logged-in user
        if (!user.comparePassword(req.body.currentPassword)) {
          return res.status(401).json({
            status: "fail",
            message: "Current password is incorrect",
          });
        }
      } else {
        // This is an admin updating a user's password, which doesn't require current password
        // Only allow if the user is an admin or has the appropriate role
        if (req.user.role !== 'systemAdmin' && req.user.id !== user.id) {
          return res.status(403).json({
            status: "fail",
            message: "You don't have permission to change this user's password without verification",
          });
        }
      }

      // Update the password
      user.password = req.body.password;

      // If an admin is resetting a user's password, set requirePasswordChange to true
      if (req.user.role === 'systemAdmin' && req.user.id !== user.id) {
        user.requirePasswordChange = true;
        console.log('Admin reset password for user, setting requirePasswordChange to true:', user.email);
      }
    }

    // Save changes
    await user.save();

    // Add audit log entry
    if (req.auditData) {
      req.auditData.userName = user.username;
      req.auditData.formId = user.id;
    }

    // Remove password from response
    const userWithoutPassword = { ...user.get() };
    delete userWithoutPassword.password;

    res.status(200).json({
      status: "success",
      data: { user: userWithoutPassword },
    });
  } catch (err) {
    console.error("Error updating user:", err);
    res.status(400).json({
      status: "fail",
      message: err.message,
    });
  }
};

// Delete a user (hard delete)
exports.deleteUser = async (req, res) => {
  try {
    // Get subsidiary from header or from authenticated user
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || req.user.sub;

    // Prepare query conditions
    let whereCondition = { id: req.params.id };

    // If user is not systemAdmin, scope to their subsidiary
    if (req.user.role !== 'systemAdmin') {
      whereCondition.sub = subsidiary;
    }

    // Find user by ID and scoped appropriately
    const user = await User.findOne({
      where: whereCondition
    });

    if (!user) {
      return res.status(404).json({
        status: "fail",
        message: "User not found",
      });
    }

    // Store user info for audit log before deletion
    if (req.auditData) {
      req.auditData.userName = user.username;
      req.auditData.formId = user.id;
    }

    console.log(`Performing hard delete for user ${user.id} (${user.email})`);

    // Store user details for response
    const userDetails = {
      id: user.id,
      email: user.email,
      username: user.username,
      sub: user.sub
    };

    // Hard delete the user
    await User.destroy({
      where: { id: user.id }
    });

    console.log(`User ${user.id} (${user.email}) has been hard deleted`);

    res.status(200).json({
      status: "success",
      message: "User deleted successfully",
      data: {
        deletedUser: userDetails
      }
    });
  } catch (err) {
    console.error("Error deleting user:", err);
    res.status(500).json({
      status: "error",
      message: err.message,
    });
  }
};
