'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add rejectionReason field to UserAccessRequests table
      await queryInterface.addColumn('UserAccessRequests', 'rejectionReason', {
        type: Sequelize.TEXT,
        allowNull: true,
      });

      console.log('rejectionReason field added to UserAccessRequests table successfully.');
    } catch (error) {
      console.error('Error adding rejectionReason field:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Remove the rejectionReason column
      await queryInterface.removeColumn('UserAccessRequests', 'rejectionReason');

      console.log('rejectionReason field removed from UserAccessRequests table successfully.');
    } catch (error) {
      console.error('Error removing rejectionReason field:', error);
      throw error;
    }
  }
};
