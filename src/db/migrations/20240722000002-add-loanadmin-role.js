'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if 'loanadmin' already exists in the enum
      const result = await queryInterface.sequelize.query(`
        SELECT EXISTS (
          SELECT 1 FROM pg_enum
          WHERE enumlabel = 'loanadmin'
          AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_user_role')
        );
      `, { type: Sequelize.QueryTypes.SELECT });

      if (!result[0].exists) {
        // Add 'loanadmin' to the existing role enum
        await queryInterface.sequelize.query(`
          ALTER TYPE "enum_user_role" ADD VALUE 'loanadmin';
        `);
        console.log('Successfully added loanadmin role to enum_user_role');
      } else {
        console.log('loanadmin role already exists in enum_user_role, skipping');
      }
    } catch (error) {
      console.error('Error adding loanadmin role:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Note: PostgreSQL doesn't support removing enum values directly
      // This would require recreating the enum type and updating all references
      console.log('Rollback: Cannot remove enum values in PostgreSQL. Manual intervention required.');
    } catch (error) {
      console.error('Error in rollback:', error);
      throw error;
    }
  }
};
