'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Get the current ENUM values for role
      const enumQuery = `
        SELECT e.enumlabel
        FROM pg_enum e
        JOIN pg_type t ON e.enumtypid = t.oid
        WHERE t.typname = 'enum_user_role'
      `;
      const [enumResults] = await queryInterface.sequelize.query(enumQuery);
      const currentEnumValues = enumResults.map(r => r.enumlabel);

      console.log('Current ENUM values:', currentEnumValues);

      // Check if we need to add new ENUM values
      const newEnumValues = ['systemAdmin', 'hr', 'it', 'supervisor'];
      const valuesToAdd = newEnumValues.filter(v => !currentEnumValues.includes(v));

      if (valuesToAdd.length > 0) {
        console.log('Adding new ENUM values:', valuesToAdd);

        // Add new values to the ENUM type
        for (const value of valuesToAdd) {
          try {
            await queryInterface.sequelize.query(`
              ALTER TYPE "enum_user_role" ADD VALUE IF NOT EXISTS '${value}';
            `);
            console.log(`Added '${value}' to enum_user_role`);
          } catch (err) {
            console.log(`Error adding '${value}' to enum: ${err.message}`);
          }
        }
      }

      // Check if role_new column already exists and get its data type
      const columnInfo = await queryInterface.sequelize.query(
        `SELECT column_name, data_type, udt_name
         FROM information_schema.columns
         WHERE table_name = 'user' AND column_name = 'role_new';`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      const columnExists = columnInfo.length > 0;

      if (!columnExists) {
        console.log('Adding role_new column to user table');
        // Create a temporary column to store the new role values
        await queryInterface.addColumn('user', 'role_new', {
          type: Sequelize.STRING,
          allowNull: true
        });

        // Update the temporary column with the new role values
        await queryInterface.sequelize.query(`
          UPDATE "user"
          SET role_new = CASE
            WHEN role = 'admin' THEN 'systemAdmin'
            WHEN role = 'it' THEN 'it'
            ELSE 'supervisor'
          END
        `);
      } else {
        console.log('role_new column already exists, skipping creation');
        console.log('Column info:', columnInfo[0]);

        // Check if the column is an enum type
        if (columnInfo[0].data_type === 'USER-DEFINED' && columnInfo[0].udt_name.startsWith('enum_')) {
          console.log('role_new is an enum type, using type casting');

          // For enum columns, we need to cast the values
          await queryInterface.sequelize.query(`
            UPDATE "user"
            SET role_new = (CASE
              WHEN role = 'admin' THEN 'systemAdmin'::text
              WHEN role = 'it' THEN 'it'::text
              ELSE 'supervisor'::text
            END)::${columnInfo[0].udt_name}
            WHERE role_new IS NULL
          `);
        } else {
          // For string columns, no casting needed
          // Check if role column already has new enum values
          const hasNewEnumValues = await queryInterface.sequelize.query(`
            SELECT COUNT(*) FROM "user" WHERE role IN ('systemAdmin', 'hr')
          `, { type: queryInterface.sequelize.QueryTypes.SELECT });

          if (parseInt(hasNewEnumValues[0].count, 10) > 0) {
            // Data already migrated, just copy current role values to role_new
            console.log('Role column already contains new enum values, copying to role_new');
            await queryInterface.sequelize.query(`
              UPDATE "user"
              SET role_new = role::text
              WHERE role_new IS NULL
            `);
          } else {
            // Original migration logic for old enum values
            await queryInterface.sequelize.query(`
              UPDATE "user"
              SET role_new = CASE
                WHEN role = 'admin' THEN 'systemAdmin'
                WHEN role = 'it' THEN 'it'
                ELSE 'supervisor'
              END
              WHERE role_new IS NULL
            `);
          }
        }
      }

      // Now update the role column with the values from role_new
      // First check if any rows need updating
      const needsUpdate = await queryInterface.sequelize.query(`
        SELECT COUNT(*) FROM "user" WHERE role_new IS NOT NULL
      `, { type: queryInterface.sequelize.QueryTypes.SELECT });

      if (parseInt(needsUpdate[0].count, 10) > 0) {
        console.log(`Updating ${needsUpdate[0].count} user roles`);

        try {
          await queryInterface.sequelize.query(`
            UPDATE "user"
            SET role = role_new::enum_user_role_new
            WHERE role_new IS NOT NULL
          `);
          console.log('Successfully updated user roles');
        } catch (error) {
          console.error('Error updating roles:', error.message);
          console.log('Trying alternative approach with individual updates...');

          // Get all users that need updating
          const usersToUpdate = await queryInterface.sequelize.query(`
            SELECT id, role_new FROM "user" WHERE role_new IS NOT NULL
          `, { type: queryInterface.sequelize.QueryTypes.SELECT });

          console.log(`Found ${usersToUpdate.length} users to update individually`);

          // Update each user individually
          for (const user of usersToUpdate) {
            try {
              await queryInterface.sequelize.query(`
                UPDATE "user" SET role = '${user.role_new}'::enum_user_role_new WHERE id = ${user.id}
              `);
              console.log(`Updated user ${user.id} to role ${user.role_new}`);
            } catch (userError) {
              console.error(`Failed to update user ${user.id}:`, userError.message);
            }
          }
        }
      } else {
        console.log('No users need role updates');
      }

      // Check if we should remove the temporary column
      try {
        await queryInterface.removeColumn('user', 'role_new');
        console.log('Removed temporary role_new column');
      } catch (error) {
        console.error('Error removing role_new column:', error.message);
        console.log('Continuing with migration...');
      }

      // Add status column if it doesn't exist
      try {
        await queryInterface.addColumn('user', 'status', {
          type: Sequelize.ENUM('active', 'inactive'),
          defaultValue: 'active',
          allowNull: false
        });
        console.log('Added status column to user table');
      } catch (err) {
        console.log('Status column may already exist:', err.message);
      }

      // Set status to 'active' for all users if it's not already set
      await queryInterface.sequelize.query(`
        UPDATE "user"
        SET status = 'active'
        WHERE status IS NULL
      `);

      console.log('Successfully updated existing user roles to new system');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Check if role_old column already exists and get its data type
      const columnInfo = await queryInterface.sequelize.query(
        `SELECT column_name, data_type, udt_name
         FROM information_schema.columns
         WHERE table_name = 'user' AND column_name = 'role_old';`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      const columnExists = columnInfo.length > 0;

      if (!columnExists) {
        console.log('Adding role_old column to user table');
        // Create a temporary column to store the old role values
        await queryInterface.addColumn('user', 'role_old', {
          type: Sequelize.STRING,
          allowNull: true
        });

        // Update the temporary column with the old role values
        await queryInterface.sequelize.query(`
          UPDATE "user"
          SET role_old = CASE
            WHEN role = 'systemAdmin' THEN 'admin'
            WHEN role = 'it' THEN 'it'
            WHEN role = 'hr' THEN 'supervisor'
            WHEN role = 'supervisor' THEN 'supervisor'
            ELSE 'supervisor'
          END
        `);
      } else {
        console.log('role_old column already exists, skipping creation');
        console.log('Column info:', columnInfo[0]);

        // Check if the column is an enum type
        if (columnInfo[0].data_type === 'USER-DEFINED' && columnInfo[0].udt_name.startsWith('enum_')) {
          console.log('role_old is an enum type, using type casting');

          // For enum columns, we need to cast the values
          await queryInterface.sequelize.query(`
            UPDATE "user"
            SET role_old = (CASE
              WHEN role = 'systemAdmin' THEN 'admin'::text
              WHEN role = 'it' THEN 'it'::text
              WHEN role = 'hr' THEN 'supervisor'::text
              WHEN role = 'supervisor' THEN 'supervisor'::text
              ELSE 'supervisor'::text
            END)::${columnInfo[0].udt_name}
            WHERE role_old IS NULL
          `);
        } else {
          // For string columns, no casting needed
          await queryInterface.sequelize.query(`
            UPDATE "user"
            SET role_old = CASE
              WHEN role = 'systemAdmin' THEN 'admin'
              WHEN role = 'it' THEN 'it'
              WHEN role = 'hr' THEN 'supervisor'
              WHEN role = 'supervisor' THEN 'supervisor'
              ELSE 'supervisor'
            END
            WHERE role_old IS NULL
          `);
        }
      }

      // Now update the role column with the values from role_old
      // First check if any rows need updating
      const needsUpdate = await queryInterface.sequelize.query(`
        SELECT COUNT(*) FROM "user" WHERE role_old IS NOT NULL AND role_old IN ('admin', 'it', 'supervisor')
      `, { type: queryInterface.sequelize.QueryTypes.SELECT });

      if (parseInt(needsUpdate[0].count, 10) > 0) {
        console.log(`Updating ${needsUpdate[0].count} user roles`);

        try {
          await queryInterface.sequelize.query(`
            UPDATE "user"
            SET role = role_old::enum_user_role_new
            WHERE role_old IS NOT NULL AND role_old IN ('admin', 'it', 'supervisor')
          `);
          console.log('Successfully updated user roles');
        } catch (error) {
          console.error('Error updating roles:', error.message);
          console.log('Trying alternative approach with individual updates...');

          // Get all users that need updating
          const usersToUpdate = await queryInterface.sequelize.query(`
            SELECT id, role_old FROM "user"
            WHERE role_old IS NOT NULL AND role_old IN ('admin', 'it', 'supervisor')
          `, { type: queryInterface.sequelize.QueryTypes.SELECT });

          console.log(`Found ${usersToUpdate.length} users to update individually`);

          // Update each user individually
          for (const user of usersToUpdate) {
            try {
              await queryInterface.sequelize.query(`
                UPDATE "user" SET role = '${user.role_old}'::enum_user_role_new WHERE id = ${user.id}
              `);
              console.log(`Updated user ${user.id} to role ${user.role_old}`);
            } catch (userError) {
              console.error(`Failed to update user ${user.id}:`, userError.message);
            }
          }
        }
      } else {
        console.log('No users need role updates');
      }

      // Check if we should remove the temporary column
      try {
        await queryInterface.removeColumn('user', 'role_old');
        console.log('Removed temporary role_old column');
      } catch (error) {
        console.error('Error removing role_old column:', error.message);
        console.log('Continuing with migration...');
      }

      console.log('Successfully reverted user roles to old system');
    } catch (error) {
      console.error('Migration reversion error:', error);
      throw error;
    }
  }
};
