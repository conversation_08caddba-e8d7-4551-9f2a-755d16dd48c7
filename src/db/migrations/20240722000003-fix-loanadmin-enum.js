'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Get all enum types that contain role information
      const enumTypes = await queryInterface.sequelize.query(`
        SELECT DISTINCT typname 
        FROM pg_type t 
        JOIN pg_enum e ON t.oid = e.enumtypid 
        WHERE typname LIKE '%role%'
        ORDER BY typname;
      `, { type: Sequelize.QueryTypes.SELECT });

      console.log('Found enum types:', enumTypes.map(t => t.typname));

      // Add 'loanadmin' to each role enum type if it doesn't exist
      for (const enumType of enumTypes) {
        const typeName = enumType.typname;
        
        // Check if 'loanadmin' already exists in this enum
        const result = await queryInterface.sequelize.query(`
          SELECT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumlabel = 'loanadmin' 
            AND enumtypid = (SELECT oid FROM pg_type WHERE typname = '${typeName}')
          );
        `, { type: Sequelize.QueryTypes.SELECT });

        if (!result[0].exists) {
          try {
            await queryInterface.sequelize.query(`
              ALTER TYPE "${typeName}" ADD VALUE 'loanadmin';
            `);
            console.log(`Successfully added loanadmin to ${typeName}`);
          } catch (error) {
            console.log(`Failed to add loanadmin to ${typeName}:`, error.message);
          }
        } else {
          console.log(`loanadmin already exists in ${typeName}, skipping`);
        }
      }
    } catch (error) {
      console.error('Error fixing loanadmin enum:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      console.log('Rollback: Cannot remove enum values in PostgreSQL. Manual intervention required.');
    } catch (error) {
      console.error('Error in rollback:', error);
      throw error;
    }
  }
};
