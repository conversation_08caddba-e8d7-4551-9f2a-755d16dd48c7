'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add NSSF field to UserAccessRequests table
      await queryInterface.addColumn('UserAccessRequests', 'nssf', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      console.log('NSSF field added to UserAccessRequests table successfully.');
    } catch (error) {
      console.error('Error adding NSSF field:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Remove the NSSF column
      await queryInterface.removeColumn('UserAccessRequests', 'nssf');

      console.log('NSSF field removed from UserAccessRequests table successfully.');
    } catch (error) {
      console.error('Error removing NSSF field:', error);
      throw error;
    }
  }
};
