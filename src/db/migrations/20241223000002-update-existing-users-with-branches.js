'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Update existing Premier Uganda users with branch assignments
    await queryInterface.sequelize.query(`
      UPDATE "user" 
      SET branch = CASE 
        WHEN email = '<EMAIL>' THEN 'Head Office'
        WHEN email = '<EMAIL>' THEN 'Head Office'
        WHEN email = '<EMAIL>' THEN 'Head Office'
        WHEN email = '<EMAIL>' THEN 'Kampala Branch'
        ELSE 'Head Office'
      END
      WHERE sub = 'premieruganda' AND branch IS NULL;
    `);

    // Update other subsidiaries with default branch assignments
    await queryInterface.sequelize.query(`
      UPDATE "user" 
      SET branch = 'Head Office'
      WHERE branch IS NULL;
    `);
  },

  async down(queryInterface, Sequelize) {
    // Remove branch assignments
    await queryInterface.sequelize.query(`
      UPDATE "user" 
      SET branch = NULL;
    `);
  }
};
