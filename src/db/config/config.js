// const dotenv = require("dotenv");
// const path = require("path");
// dotenv.config({ path: path.join(__dirname, "../../.env") });
require("dotenv").config();

module.exports = {
  development: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DBNAME,
    host: process.env.DB_HOST,
    dialect: "postgres",
    quoteIdentifiers: false,
    freezeTableName: true,
    logging: true,
    // encrypt: process.env.DB_ENCRYPT,
    // pool: {
    //   max: parseInt(process.env.DB_POOL_MAX),
    //   min: parseInt(process.env.DB_POOL_MIN),
    //   acquire: parseInt(process.env.DB_POOL_ACQUIRE),
    //   idle: parseInt(process.env.DB_POOL_IDLE),
    // },
  },

  production: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: "digital_user_access",
    host: process.env.DB_HOST,
    dialect: "postgres",
    quoteIdentifiers: false,
    freezeTableName: true,
    logging: false, // Disable logging in production for performance
    pool: {
      max: 20, // Maximum number of connections in pool
      min: 5,  // Minimum number of connections in pool
      acquire: 30000, // Maximum time to get connection before throwing error
      idle: 10000, // Maximum time connection can be idle before being released
    },
    dialectOptions: {
      connectTimeout: 60000, // Connection timeout
      requestTimeout: 30000, // Request timeout
    },
  },
};
