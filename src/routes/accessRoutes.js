const express = require("express");
const axios = require("axios");
const multer = require("multer");
const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });
const { uploadExcel } = require("../_middlewares/upload");

const auditTrail = require("../_middlewares/auditTrail");
const populateAuditData = require("../_middlewares/populateAuditData");
const { UserAccessRequest } = require("../db/models");
const {
  approveBranchHead,
  approveHR,
  approveExecutive,
  approveIT,
  rejectAccessRequest,
  dataMiddleware,
  generateAccessRequestReport,
  validatePhoneNumber,
} = require("../controllers/accessFormController");
const { sendEmail } = require("#src/utils/emailService");

const approverEmails = require("../config/approverEmails");

require("dotenv").config();

const SUBSIDIARY_CONFIG = {
  platinumkenya: {
    authUrl: "https://ws.platinumcredit.co.ke/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.ke/api/dimension",
    origin: "https://ws.platinumcredit.co.ke",
  },
  premierkenya: {
    authUrl: "https://cache.premiergroup.co.ke/ws/auth/gettoken",
    apiUrl: "https://cache.premiergroup.co.ke/api/dimension",
    origin: "https://cache.premiergroup.co.ke",
  },
  momentumcredit: {
    authUrl: "https://ws.momentumcredit.co.ke/ws/auth/gettoken",
    apiUrl: "https://ws.momentumcredit.co.ke/api/dimension",
    origin: "https://ws.momentumcredit.co.ke",
  },
  platinumtanzania: {
    authUrl: "https://ws.platinumcredit.co.tz/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.tz/api/dimension",
    origin: "https://ws.platinumcredit.co.tz",
  },
  premierfanikiwa: {
    authUrl: "https://ws.fmfc.co.tz/ws/auth/gettoken",
    apiUrl: "https://ws.fmfc.co.tz/api/dimension",
    origin: "https://ws.fmfc.co.tz",
  },
  spectrumzambia: {
    authUrl: "https://cache.spectrumcreditltd.com/ws/auth/gettoken",
    apiUrl: "https://cache.spectrumcreditltd.com/api/dimension",
    origin: "https://cache.spectrumcreditltd.com",
  },
  premiersouthafrica: {
    authUrl: "https://ws.premiercredit.co.za/ws/auth/gettoken",
    apiUrl: "https://ws.premiercredit.co.za/api/dimension",
    origin: "https://ws.premiercredit.co.za",
  },
  platinumuganda: {
    authUrl: "https://ws.platinumcredit.co.ug/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.ug/api/dimension",
    origin: "https://ws.platinumcredit.co.ug",
  },
  premieruganda: {
    authUrl: "https://ws.premiercredit.co.ug/ws/auth/gettoken",
    apiUrl: "https://ws.premiercredit.co.ug/api/dimension",
    origin: "https://ws.premiercredit.co.ug",
  },
};

const getSubsidiaryFromHeader = (req) => {
  const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";
  console.log(`getSubsidiaryFromHeader: Extracted subsidiary '${subsidiary}' from header '${req.headers["x-subsidiary"]}'`);
  return subsidiary;
};

const getApiToken = async (subsidiaryKey) => {
  const config = SUBSIDIARY_CONFIG[subsidiaryKey];
  if (!config) throw new Error(`Unknown subsidiary: ${subsidiaryKey}`);

  console.log(`Getting API token for ${subsidiaryKey} from ${config.authUrl}`);
  console.log(`Using bearer token: ${process.env.DIMENSION_API_TOKEN_PLATKE ? 'Available' : 'Missing'}`);

  try {
    const response = await axios.post(
      config.authUrl,
      process.env.DIMENSION_API_TOKEN_PLATKE,
      {
        headers: {
          Authorization: `Bearer ${process.env.DIMENSION_API_TOKEN_PLATKE}`,
          Accept: "application/json",
          "Content-Type": "application/json",
          Origin: config.origin,
        },
      }
    );

    console.log(`Successfully got token for ${subsidiaryKey}`);
    return {
      token: response.data,
      origin: config.origin,
      apiUrl: config.apiUrl,
    };
  } catch (error) {
    console.error(
      `Error fetching token for ${subsidiaryKey}:`,
      error?.response?.data || error.message
    );
    console.error(`Request URL: ${config.authUrl}`);
    console.error(`Origin: ${config.origin}`);
    throw new Error(`Failed to fetch API token for ${subsidiaryKey}`);
  }
};



const fetchDimensionData = async (req, res, endpoint, entityName) => {
  const subsidiary = getSubsidiaryFromHeader(req);
  try {
    console.log(`Fetching ${entityName} for subsidiary: ${subsidiary}`);

    // Get API token and configuration
    const { token, origin, apiUrl } = await getApiToken(subsidiary);

    if (!apiUrl) {
      console.error(`Missing apiUrl for subsidiary: ${subsidiary}`);
      return res.status(500).json({
        status: "error",
        message: `Configuration error for subsidiary: ${subsidiary}`
      });
    }

    console.log(`Making request to: ${apiUrl}/${endpoint}`);

    const response = await axios.get(`${apiUrl}/${endpoint}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
        Origin: origin,
      },
      timeout: 10000 // 10 second timeout
    });

    console.log(`Successfully fetched ${entityName} for ${subsidiary}`);
    return res.json(response.data);
  } catch (error) {
    // Log detailed error information
    console.error(`Error fetching ${entityName} for ${subsidiary}:`);
    console.error(`Request URL: ${SUBSIDIARY_CONFIG[subsidiary]?.apiUrl}/${endpoint}`);
    console.error(`Error details:`, error?.response?.data || error.message);

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
      console.error(`Network error connecting to ${SUBSIDIARY_CONFIG[subsidiary]?.apiUrl}: ${error.code}`);
    }

    return res.status(500).json({
      status: "error",
      message: `Failed to fetch ${entityName} for ${subsidiary}`,
      error: error.message
    });
  }
};



// Debug endpoint to check user subsidiary access
router.get("/debug-user", async (req, res) => {
  try {
    console.log("Debug user endpoint called");
    console.log("User from JWT:", req.user);
    console.log("Headers:", req.headers);

    if (!req.user) {
      return res.json({ error: "No user authenticated" });
    }

    const { Tenant } = require("../db/models");
    const userTenant = await Tenant.findOne({
      where: { id: req.user.subId },
    });

    return res.json({
      user: {
        id: req.user.id,
        username: req.user.username,
        email: req.user.email,
        role: req.user.role,
        sub: req.user.sub,
        subId: req.user.subId
      },
      tenant: userTenant,
      requestedSubsidiary: req.headers["x-subsidiary"]
    });
  } catch (error) {
    console.error("Debug user error:", error);
    return res.status(500).json({ error: error.message });
  }
});

router.get("/branches", (req, res) =>
  fetchDimensionData(req, res, "branches", "branches")
);
router.get("/userroles", (req, res) =>
  fetchDimensionData(req, res, "roles", "roles")
);
router.get("/departments", (req, res) =>
  fetchDimensionData(req, res, "departments", "departments")
);

router.post(
  "/",
  upload.fields([
    { name: "cv" },
    { name: "id" },
    { name: "kraPin" },
    { name: "nssf" },
    { name: "sha" },
    { name: "bankDetails" },
    { name: "passportImage" },
    { name: "contract" },
    { name: "localGovId" },
    { name: "refereesLetter" },
    { name: "personalInfoForm" },
    { name: "passportSizes" },
    { name: "applicationLetter" },
    { name: "tin" },
    { name: "otherDocuments" }, // For Premier Uganda multiple file uploads
  ]),
  populateAuditData,
  auditTrail("user-request-access"),
  async (req, res) => {
    try {
      const subId = getSubsidiaryFromHeader(req);
      let { accessType } = req.body;
      if (!Array.isArray(accessType)) accessType = [accessType];

      const {
        firstName,
        middleName,
        lastName,
        nin,
        dateOfBirth,
        nextOfKinName,
        nextOfKinMobile,
        nextOfKinRelationship,
        nextOfKinDependents,
        tin,
        nssf,
        email,
        systemName,
        branch,
        department,
        telephone,
        role,
        previousRole,
        reason,
        staffId,
        position,
        startDate,
      } = req.body;

      // Phone number uniqueness check removed - allow duplicate phone numbers
      if (telephone) {
        console.log(`✅ Route handler - phone number validation disabled, allowing: ${telephone}`);
      }

      // Process attachments first
      const attachments = [];
      if (req.files) {
        console.log('Processing files:', Object.keys(req.files));
        for (const [key, files] of Object.entries(req.files)) {
          // Handle multiple files for otherDocuments (Premier Uganda)
          if (key === 'otherDocuments' && Array.isArray(files)) {
            files.forEach(file => {
              console.log(`Processing multiple file for key ${key}:`, file.originalname, file.mimetype);
              attachments.push({
                filename: file.originalname,
                content: file.buffer.toString("base64"),
                encoding: "base64",
                contentType: file.mimetype || "application/octet-stream",
              });
            });
          } else {
            // Handle single file for other fields
            const file = files[0];
            if (file) {
              console.log(`Processing single file for key ${key}:`, file.originalname, file.mimetype);
              attachments.push({
                filename: file.originalname,
                content: file.buffer.toString("base64"),
                encoding: "base64",
                contentType: file.mimetype || "application/octet-stream",
              });
            }
          }
        }
      }
      console.log('Total attachments processed:', attachments.length);

      // Helper function to validate and convert date
      const validateDate = (dateString) => {
        if (!dateString || dateString === '' || dateString === 'Invalid date') {
          return null;
        }
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return null;
        }
        return date;
      };

      // Helper function to handle optional string fields
      const validateOptionalString = (value) => {
        return (value && value.trim() !== '') ? value.trim() : null;
      };

      // Helper function to handle email field (optional for Premier Uganda, required for others)
      const validateEmailBySubsidiary = (value, subsidiary) => {
        if (value && value.trim() !== '') {
          return value.trim();
        }
        // For Premier Uganda, email is optional - return null if not provided
        if (subsidiary === 'premieruganda') {
          return null;
        }
        // For all other subsidiaries, email is required - return placeholder if not provided
        return '<EMAIL>';
      };

      // Base request data that applies to all subsidiaries
      const requestData = {
        firstName,
        lastName,
        email: validateEmailBySubsidiary(email, subId),
        systemName,
        branch,
        department,
        telephone,
        accessType,
        role,
        previousRole: validateOptionalString(previousRole),
        reason,
        subId,
        attachments, // Include attachments in the database record
      };

      // Add Premier Uganda specific fields only for premieruganda subsidiary
      if (subId === 'premieruganda') {
        // Validate required fields for Premier Uganda
        if (!tin || tin.trim() === '') {
          return res.status(400).json({
            error: "TIN is required for Premier Uganda"
          });
        }
        if (!nssf || nssf.trim() === '') {
          return res.status(400).json({
            error: "NSSF Number is required for Premier Uganda"
          });
        }

        requestData.middleName = validateOptionalString(middleName);
        requestData.nin = validateOptionalString(nin);
        requestData.dateOfBirth = validateDate(dateOfBirth);
        requestData.nextOfKinName = nextOfKinName;
        requestData.nextOfKinMobile = nextOfKinMobile;
        requestData.nextOfKinRelationship = nextOfKinRelationship;
        requestData.nextOfKinDependents = nextOfKinDependents;
        requestData.tin = tin.trim(); // Required field, no need for optional validation
        requestData.nssf = nssf.trim(); // Required field, no need for optional validation
        requestData.staffId = validateOptionalString(staffId);
        requestData.position = validateOptionalString(position);
        requestData.startDate = validateDate(startDate);
      }

      const request = await UserAccessRequest.create(requestData);

      const approvers = approverEmails[subId];

      if (!approvers) {
        return res.status(400).json({ error: "Invalid subsidiary" });
      }

      sendEmail({
        to: approvers.HR,
        subject: "New Access Request Pending Approval",
        tenant: subId,
        emailBody: `
          <p>Dear HR,</p>
          <p>A new access request has been submitted and is awaiting your approval.</p>
          <table style="border-collapse: collapse; width: auto;">
            <tr><td style="font-weight: bold;">Access Requestor:</td><td>${firstName} ${lastName}</td></tr>
            <tr><td style="font-weight: bold;">Email:</td><td><a href="mailto:${email}">${email}</a></td></tr>
            <tr><td style="font-weight: bold;">System Name:</td><td>${systemName}</td></tr>
            <tr><td style="font-weight: bold;">Branch:</td><td>${branch}</td></tr>
            <tr><td style="font-weight: bold;">Department:</td><td>${department}</td></tr>
            <tr><td style="font-weight: bold;">Access Type:</td><td>${accessType.join(", ")}</td></tr>
            <tr><td style="font-weight: bold;">Role:</td><td>${role}</td></tr>
            <tr><td style="font-weight: bold;">Reason:</td><td>${reason || "N/A"}</td></tr>
          </table>
          <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/approval/${subId}" target="_blank">Click here to review and approve this request</a></p>
        `,
        tenant: subId,
        attachments, // Include attachments in the email
      });

      return res.status(201).json(request);
    } catch (error) {
      console.error("Error Creating Access Request:", error);
      return res.status(500).json({
        error: "Internal Server Error",
        details: error.message,
      });
    }
  }
);

// Bulk upload endpoint for Premier Uganda
router.post("/bulk-upload/:subsidiary", uploadExcel, async (req, res) => {
  try {
    const subsidiary = req.params.subsidiary;

    if (subsidiary !== 'premieruganda') {
      return res.status(400).json({
        error: "Bulk upload is only available for Premier Uganda"
      });
    }

    if (!req.file) {
      return res.status(400).json({
        error: "No file uploaded"
      });
    }

    const XLSX = require('xlsx');
    const workbook = XLSX.readFile(req.file.path);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2; // Excel row number (accounting for header)

      try {
        // Validate required fields
        const requiredFields = [
          'FirstName', 'LastName', 'NIN', 'DateOfBirth', 'MobilePhone',
          'NextOfKinName', 'NextOfKinMobile', 'NextOfKinRelationship',
          'NextOfKinDependents', 'TIN', 'NSSF', 'Branch', 'SystemName', 'AccessType', 'Reason'
        ];

        const missingFields = requiredFields.filter(field => !row[field]);
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate NIN format (14 characters and specific format for Premier Uganda)
        if (row.NIN) {
          if (row.NIN.length !== 14) {
            throw new Error(`NIN must be exactly 14 characters, got ${row.NIN.length}`);
          }
          // Premier Uganda NIN format: 14 characters starting with CM (male) or CF (female)
          // Format: CM/CF + 12 alphanumeric characters
          const ninPattern = /^(CM|CF)[A-Z0-9]{12}$/;
          if (!ninPattern.test(row.NIN.toUpperCase())) {
            throw new Error(`NIN format is invalid. Must start with CM or CF followed by 12 alphanumeric characters`);
          }
        }

        // Helper function to validate and convert date
        const validateDate = (dateString) => {
          if (!dateString || dateString === '' || dateString === 'Invalid date') {
            return null;
          }
          const date = new Date(dateString);
          if (isNaN(date.getTime())) {
            return null;
          }
          return date;
        };

        // Helper function to handle optional string fields
        const validateOptionalString = (value) => {
          return (value && value.trim() !== '') ? value.trim() : null;
        };

        // Helper function to handle email field (optional for Premier Uganda, required for others)
        const validateEmailBySubsidiary = (value, subsidiary) => {
          if (value && value.trim() !== '') {
            return value.trim();
          }
          // For Premier Uganda, email is optional - return null if not provided
          if (subsidiary === 'premieruganda') {
            return null;
          }
          // For all other subsidiaries, email is required - return placeholder if not provided
          return '<EMAIL>';
        };

        // Create request data object
        const requestData = {
          firstName: row.FirstName,
          lastName: row.LastName,
          email: validateEmailBySubsidiary(row.Email, subsidiary),
          telephone: row.MobilePhone,
          branch: row.Branch,
          systemName: row.SystemName,
          department: '', // Will be filled by HR in approval flow
          accessType: row.AccessType,
          role: '', // Will be filled by HR in approval flow
          reason: row.Reason,
          approvalStatus: "Pending",
          subId: subsidiary,
          attachments: []
        };

        // Add Premier Uganda specific fields
        if (subsidiary === 'premieruganda') {
          requestData.middleName = validateOptionalString(row.MiddleName);
          requestData.nin = validateOptionalString(row.NIN);
          requestData.dateOfBirth = validateDate(row.DateOfBirth);
          requestData.nextOfKinName = row.NextOfKinName;
          requestData.nextOfKinMobile = row.NextOfKinMobile;
          requestData.nextOfKinRelationship = row.NextOfKinRelationship;
          requestData.nextOfKinDependents = row.NextOfKinDependents;
          requestData.tin = row.TIN.trim(); // Required field
          requestData.nssf = row.NSSF.trim(); // Required field
        }

        // Create access request
        const accessRequest = await UserAccessRequest.create(requestData);

        results.push({
          row: rowNumber,
          success: true,
          message: `Access request created successfully for ${row.FirstName} ${row.LastName}`
        });
        successCount++;

      } catch (error) {
        console.error(`Error processing row ${rowNumber}:`, error);
        results.push({
          row: rowNumber,
          success: false,
          message: error.message
        });
        errorCount++;
      }
    }

    // Clean up uploaded file
    const fs = require('fs');
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.json({
      message: `Bulk upload completed. ${successCount} successful, ${errorCount} errors.`,
      successCount,
      errorCount,
      results
    });

  } catch (error) {
    console.error("Bulk upload error:", error);
    res.status(500).json({
      error: "Bulk upload failed",
      details: error.message
    });
  }
});

// Report endpoint for auditors and IT personnel
router.get("/report", generateAccessRequestReport);

// Phone number validation endpoint
router.get("/validate-phone", validatePhoneNumber);



// Update HR fields for Premier Uganda
router.put("/:id/hr-fields", async (req, res) => {
  try {
    const requestId = req.params.id;
    const { staffId, position, department, startDate } = req.body;
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();

    if (subsidiary !== 'premieruganda') {
      return res.status(400).json({
        error: "HR fields update is only available for Premier Uganda"
      });
    }

    // Validate required fields
    if (!staffId || !position || !department || !startDate) {
      return res.status(400).json({
        error: "All HR fields are required: staffId, position, department, startDate"
      });
    }

    // Update the access request with HR fields
    const [updatedRows] = await UserAccessRequest.update({
      staffId,
      position,
      department,
      startDate
    }, {
      where: { id: requestId, subId: subsidiary }
    });

    if (updatedRows === 0) {
      return res.status(404).json({
        error: "Access request not found or not authorized"
      });
    }

    res.json({
      message: "HR fields updated successfully",
      data: { staffId, position, department, startDate }
    });

  } catch (error) {
    console.error("Error updating HR fields:", error);
    res.status(500).json({
      error: "Failed to update HR fields",
      details: error.message
    });
  }
});

router.get("/", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);
    console.log(`Fetching access requests for subsidiary: ${subId}`);
    console.log(`Request headers:`, req.headers);

    // For premierfanikiwa subsidiary, we need to exclude middleName field
    // since the migration hasn't been applied yet
    let requests;
    if (subId === 'premierfanikiwa') {
      console.log('Using premierfanikiwa specific query (excluding Premier Uganda fields)');
      // Get all columns except middleName and other Premier Uganda specific fields
      requests = await UserAccessRequest.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]],
        attributes: [
          'id', 'subId', 'systemName', 'branch', 'firstName', 'lastName',
          'email', 'telephone', 'department', 'accessType', 'role',
          'previousRole', 'reason', 'attachments', 'approvalStatus',
          'createdAt', 'updatedAt'
        ]
      });
    } else {
      console.log('Using standard query for subsidiary:', subId);
      // For other subsidiaries, get all fields
      requests = await UserAccessRequest.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]]
      });
    }

    console.log(`Successfully fetched ${requests.length} requests for subsidiary: ${subId}`);
    return res.json({ data: requests });
  } catch (error) {
    console.error("Error Fetching Requests:", error);
    console.error("Error details:", error.message);
    console.error("Error stack:", error.stack);

    // Check if it's a database connection error
    if (error.name === 'SequelizeConnectionError') {
      return res.status(500).json({
        error: "Database connection error",
        details: "Could not connect to the database. Please check your database connection."
      });
    }

    // Check if it's a database query error
    if (error.name === 'SequelizeDatabaseError') {
      return res.status(500).json({
        error: "Database query error",
        details: error.message
      });
    }

    return res.status(500).json({
      error: "Internal Server Error",
      details: error.message
    });
  }
});

router.get("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });

    // For premierfanikiwa subsidiary, filter out Premier Uganda specific fields
    // since the migration hasn't been applied yet
    if (request.subId === 'premierfanikiwa') {
      const filteredRequest = {
        id: request.id,
        subId: request.subId,
        systemName: request.systemName,
        branch: request.branch,
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        telephone: request.telephone,
        department: request.department,
        accessType: request.accessType,
        role: request.role,
        previousRole: request.previousRole,
        reason: request.reason,
        attachments: request.attachments,
        approvalStatus: request.approvalStatus,
        createdAt: request.createdAt,
        updatedAt: request.updatedAt
      };
      return res.json(filteredRequest);
    } else {
      return res.json(request);
    }
  } catch (error) {
    console.error("Error Fetching Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.put("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });
    await request.update(req.body);
    return res.json(request);
  } catch (error) {
    console.error("Error Updating Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.delete("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });
    await request.destroy();
    return res.json({ message: "Request Deleted Successfully" });
  } catch (error) {
    console.error("Error Deleting Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.put(
  "/:id/approve-branch-head",
  dataMiddleware,
  auditTrail("branch-head-approval"),
  approveBranchHead
);
router.put(
  "/:id/approve-hr",
  dataMiddleware,
  auditTrail("hr-approval"),
  approveHR
);
router.put(
  "/:id/approve-executive",
  dataMiddleware,
  auditTrail("executive-approval"),
  approveExecutive
);
router.put(
  "/:id/approve-it",
  dataMiddleware,
  auditTrail("IT-approval"),
  approveIT
);
router.put("/:id/reject", rejectAccessRequest, auditTrail("rejection"));

// Return request to admin for editing
router.put("/:id/return-to-admin", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) {
      return res.status(404).json({ error: "Access request not found" });
    }

    const { returnReason } = req.body;
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || request.subId || "platinumkenya";

    // Update request status to indicate it's returned for editing
    request.approvalStatus = "Returned for Editing";
    request.returnReason = returnReason || "Request returned for corrections";
    await request.save();

    // Send notification email to the original requestor
    const { sendEmail } = require("#src/utils/emailService");
    const approverEmails = require("../config/approverEmails");
    const emails = approverEmails[subsidiary];

    if (emails && request.email) {
      await sendEmail({
        to: request.email,
        subject: `Access Request Returned for Editing - ${request.firstName} ${request.lastName}`,
        html: `
          <h3>Access Request Returned for Editing</h3>
          <p>Your access request has been returned for editing by HR.</p>
          <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><td><strong>Request ID:</strong></td><td>${request.id}</td></tr>
            <tr><td><strong>Name:</strong></td><td>${request.firstName} ${request.lastName}</td></tr>
            <tr><td><strong>System:</strong></td><td>${request.systemName}</td></tr>
            <tr><td><strong>Reason for Return:</strong></td><td>${returnReason || "Please review and correct the information"}</td></tr>
          </table>
          <p>Please review your request and resubmit with the necessary corrections.</p>
          <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/request-form/${subsidiary}" target="_blank">Click here to submit a new request</a></p>
          <p>Best regards,<br>HR Department</p>`,
        tenant: subsidiary,
      });
    }

    res.status(200).json({
      message: "Request returned to admin for editing",
      request
    });
  } catch (error) {
    console.error("Error returning request to admin:", error);
    res.status(500).json({
      error: "Failed to return request to admin",
      details: error.message
    });
  }
});

// Update request details (for admin editing)
router.put("/:id/update-details", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) {
      return res.status(404).json({ error: "Access request not found" });
    }

    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || request.subId || "platinumkenya";
    const {
      firstName,
      middleName,
      lastName,
      email,
      telephone,
      nin,
      dateOfBirth,
      tin,
      nssf,
      nextOfKinName,
      nextOfKinMobile,
      nextOfKinRelationship,
      nextOfKinDependents,
      systemName,
      branch,
      department,
      accessType,
      role,
      previousRole,
      reason,
      staffId,
      position,
      startDate
    } = req.body;

    // Update the request with new details
    const updateData = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (middleName !== undefined) updateData.middleName = middleName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (email !== undefined) updateData.email = email;
    if (telephone !== undefined) updateData.telephone = telephone;
    if (nin !== undefined) updateData.nin = nin;
    if (dateOfBirth !== undefined) updateData.dateOfBirth = dateOfBirth;
    if (tin !== undefined) updateData.tin = tin;
    if (nssf !== undefined) updateData.nssf = nssf;
    if (nextOfKinName !== undefined) updateData.nextOfKinName = nextOfKinName;
    if (nextOfKinMobile !== undefined) updateData.nextOfKinMobile = nextOfKinMobile;
    if (nextOfKinRelationship !== undefined) updateData.nextOfKinRelationship = nextOfKinRelationship;
    if (nextOfKinDependents !== undefined) updateData.nextOfKinDependents = nextOfKinDependents;
    if (systemName !== undefined) updateData.systemName = systemName;
    if (branch !== undefined) updateData.branch = branch;
    if (department !== undefined) updateData.department = department;
    if (accessType !== undefined) updateData.accessType = accessType;
    if (role !== undefined) updateData.role = role;
    if (previousRole !== undefined) updateData.previousRole = previousRole;
    if (reason !== undefined) updateData.reason = reason;
    if (staffId !== undefined) updateData.staffId = staffId;
    if (position !== undefined) updateData.position = position;
    if (startDate !== undefined) updateData.startDate = startDate;

    // Reset status to Pending after editing
    updateData.approvalStatus = "Pending";
    updateData.returnReason = null;

    // Ensure subId is set for validation
    updateData.subId = subsidiary;

    const [updatedRows] = await UserAccessRequest.update(updateData, {
      where: { id: req.params.id, subId: subsidiary }
    });

    if (updatedRows === 0) {
      return res.status(404).json({
        error: "Access request not found or not authorized"
      });
    }

    // Get the updated request
    const updatedRequest = await UserAccessRequest.findByPk(req.params.id);

    res.json({
      message: "Request details updated successfully",
      request: updatedRequest
    });

  } catch (error) {
    console.error("Error updating request details:", error);
    res.status(500).json({
      error: "Failed to update request details",
      details: error.message
    });
  }
});

module.exports = router;
