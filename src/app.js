const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
// const { sequelize } = require("./config/db");
const accessRoutes = require("./routes/accessRoutes");
const auditLogRoutes = require("./routes/auditLogRoutes");
const reportsRoutes = require("./routes/reportRoutes");
const dashboardRoutes = require("./routes/dashboardRoutes");
const revocationRoutes = require("./routes/revocationRoutes");
const approvalRoutes = require("./routes/approvalRoutes");
const authRoutes = require("./routes/auth");
const path = require("path");
const morgan = require("morgan");
const session = require("./utils/session");
const readSession = require("./utils/readSession");
const index = require("./db/models/index.js");
const authJwt = require("./_middlewares/authJwt");
const subsidiaryCheck = require("./_middlewares/subsidiaryCheck");

dotenv.config();

const app = express();

// CORS configuration for production
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      'https://digital-us.platcorpgroup.com',
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:3000',
      'http://localhost:7080',
      'http://localhost:4173',
      'http://localhost:8080',
      'http://127.0.0.1:5173',
      'http://127.0.0.1:5174',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:4173',
      'http://127.0.0.1:8080'
    ];

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(null, true); // Allow all origins in production for now
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'x-subsidiary',
    'mambuUser'
  ],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  maxAge: 86400 // 24 hours
};

app.use(cors(corsOptions));

// Handle preflight requests
app.options('*', cors(corsOptions));

app.use(morgan("dev"));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Serve signature files
app.use("/user-access/v1/uploads", express.static(path.join(__dirname, "../uploads")));
app.use("/user-access/v1/static", express.static(path.join(__dirname, "../public/images")));

const frontendPath = path.join(__dirname, "../public/dist");
app.use(express.static(frontendPath));


const sessions = {};

app.get("/user-access/app-definition/", (req, res) => {
  res.status(200).sendFile(path.join(__dirname, "../public/dev.xml"));
});

app.post("/user-access/ui", (req, res, next) => {
  console.log(req.body);
  const _signed = req.body.signed_request;
  session(_signed);
  req.isrequestvalid = true;
  const signedpart1 = _signed.split(".")[0];
  req.signedToken = signedpart1;
  req.method = "GET";
  next();
});
// Middleware to populate audit data
// Special route for login page - always serve the Vue app without session check
app.get("/user-access/ui/login", (_, res) => {
  console.log("Login page requested");
  res.sendFile(path.join(__dirname, "../public/dist/index.html"));
});

app.use(
  "/user-access/ui",
  (req, res, next) => {
    console.log("Session check for path:", req.path);

    // Skip session check for login page
    if (req.path === "/login") {
      console.log("Login page detected, serving without session check");
      return res.sendFile(path.join(__dirname, "../public/dist/index.html"));
    }

    if (req.isrequestvalid) {
      let expiry = new Date().getTime();
      expiry += 1000 * 2;
      sessions[req.signedToken] = { expiresIn: expiry };
    }
    const mambuUser = JSON.parse(
      readSession() || JSON.stringify({ session: "" }),
    ).session;
    const sess = sessions[mambuUser.split(".")[0]];
    console.log("Session", sess);
    const mambuUserSession = req.header("mambuUser");

    if (sess || mambuUserSession) {
      if ((sess && sess.expiresIn > new Date().getTime()) || mambuUserSession) {
        next();
      } else {
        return res
          .status(404)
          .sendFile(path.join(__dirname, "../public/error404.html"));
      }
    } else {
      return res
        .status(404)
        .sendFile(path.join(__dirname, "../public/error404.html"));
    }
  },

  express.static(path.join(__dirname, "../public/dist")),
);

// Apply JWT authentication and subsidiary check to all API routes except auth
// Auth routes need to be excluded as they handle login/registration
const apiPrefix = "/user-access/v1";
const apiMiddleware = [authJwt, subsidiaryCheck];

// Auth routes (no subsidiary check needed for authentication)
app.use(`${apiPrefix}/auth`, authRoutes);

// Protected API routes with subsidiary check
app.use(`${apiPrefix}/dashboard`, apiMiddleware, dashboardRoutes);
app.use(`${apiPrefix}/reports`, apiMiddleware, reportsRoutes);
app.use(`${apiPrefix}/access-request`, apiMiddleware, accessRoutes);
app.use(`${apiPrefix}/approval`, apiMiddleware, approvalRoutes);
app.use(`${apiPrefix}/audit-logs`, apiMiddleware, auditLogRoutes);
app.use(`${apiPrefix}/revocation`, apiMiddleware, revocationRoutes);
app.use(`${apiPrefix}/tenant`, apiMiddleware, require("./routes/tenantRoute"));
app.use(`${apiPrefix}/user`, apiMiddleware, require("./routes/userRoute"));
app.use(`${apiPrefix}/create-tenant`, apiMiddleware, require("./routes/tenantRoute"));

index.sequelize.sync({ force: false }).then(() => {
  console.log("Database connected");
});

// Handle 404 errors
app.use((_, res) => {
  res.status(404).send({ error: "Route Not Found" });
});

// Handle other errors globally
app.use((err, _, res, __) => {
  console.error(err);
  res.status(500).send({ error: "Something went wrong!" });
});

module.exports = app;
