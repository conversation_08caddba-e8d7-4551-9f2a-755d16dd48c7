const { AuditLog } = require("../db/models");
const approverEmails = require("../config/approverEmails");

/**
 * Audit Trail Middleware.
 * Logs user activity for authorization purposes, scoped by subsidiary.
 * @param {string} path - The action identifier (e.g., 'hr-approval').
 * @returns {Function} - Express middleware function.
 */
const auditTrail = (path) => {
  return async (req, res, next) => {
    try {
      if (!req.auditData) {
        console.warn(
          "Warning: req.auditData is undefined. Ensure a preceding middleware sets it."
        );
        req.auditData = {}; // Prevents undefined reference
      }

      const { userName, action, systemName, formId, formType, role, branch } =
        req.auditData;

      // Pull subId from request headers
      const subId =
        req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";

      // Get approver emails for this subsidiary
      const subsidiaryApprovers = approverEmails[subId] || {};

      // Validate formType against accepted values
      const validFormTypes = ["access", "revocation", "approval"];
      const validatedFormType = validFormTypes.includes(formType)
        ? formType
        : "access";

      // Determine the approver based on the path
      let approverRole = '';
      let approverEmail = '';

      if (path === "hr-approval") {
        approverRole = "HR";
        approverEmail = subsidiaryApprovers.HR || '';
      } else if (path === "executive-approval") {
        approverRole = "EXECUTIVE";
        approverEmail = subsidiaryApprovers.EXECUTIVE || '';
      } else if (path === "IT-approval") {
        approverRole = "IT";
        approverEmail = subsidiaryApprovers.IT || '';
      }

      // Create approver info with both role and email
      const approverInfo = approverEmail ?
        [`${approverRole} (${approverEmail})`] :
        (approverRole ? [approverRole] : []);

      const data = {
        userName: userName || "Anonymous",
        action: action || "Access Request",
        formType: validatedFormType,
        formId: formId || Math.floor(Math.random() * 100000),
        systemName: systemName || "Unknown System",
        branch: branch || "Unknown Branch",
        role: role || "Unknown Role",
        subId: subId,
        approvers: approverInfo,
        ip: req.ip || req.headers["x-forwarded-for"] || "Unknown IP",
        path: path,
        method: req.method,
        agent: req.headers["user-agent"] || "Unknown User-Agent",
        timestamp: new Date()
      };

      console.log("Audit Log Payload:", data);

      await AuditLog.create(data);
      next();
    } catch (error) {
      console.error("Audit Trail Middleware Error:", error.message);
      next(); // Continue even if audit fails
    }
  };
};

module.exports = auditTrail;
