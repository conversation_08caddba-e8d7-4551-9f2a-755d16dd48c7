# Production Issues Fix Guide

## Issues Identified

1. **Database Schema Mismatch**: The audit log model was mapping `createdAt`/`updatedAt` to lowercase column names, but the database has proper case columns.
2. **Missing Migrations**: Production database might not have the latest migrations applied.
3. **Performance Issues**: Multiple database queries in middleware causing slow loading.

## Fixes Applied

### 1. Fixed Audit Log Model (✅ COMPLETED)
- Fixed timestamp column mapping in `src/db/models/auditLog.js`
- Changed from `createdat`/`updatedat` to `createdAt`/`updatedAt`

## Steps to Fix Production

### 1. Run Database Migrations
```bash
# Connect to your production server
# Navigate to the application directory
cd /path/to/your/app

# Run pending migrations
npx sequelize-cli db:migrate

# Check migration status
npx sequelize-cli db:migrate:status
```

### 2. Restart the Application
```bash
# If using PM2
pm2 restart digital-user-access-system

# If using Docker
docker-compose restart

# If using systemd
sudo systemctl restart your-app-service
```

### 3. Verify the Fix
1. Check audit logs endpoint: `GET /user-access/v1/audit-logs`
2. Check approval flow: `GET /user-access/v1/access-request`
3. Monitor server logs for any remaining errors

## Expected Results

- ✅ Audit logs should load without 500 errors
- ✅ Approval flow should load faster and show pending requests
- ✅ No more database column mapping errors

## If Issues Persist

1. Check server logs for specific error messages
2. Verify database connection and credentials
3. Ensure all migrations have been applied successfully
4. Check if the database user has proper permissions

## Database Schema Verification

You can verify the schema is correct by running:
```sql
-- Check audit_logs table structure
\d audit_logs;

-- Check if timestamp columns exist
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'audit_logs' 
AND column_name IN ('createdAt', 'updatedAt');
```
