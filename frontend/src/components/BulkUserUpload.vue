<template>
  <div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-lg font-semibold mb-4">Bulk User Upload</h3>

    <div class="mb-4">
      <div class="bg-gray-100 p-3 rounded text-xs font-mono">
        FirstName,LastName,Email,Password,UserRole,AccountState,Branch
      </div>
    </div>

    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Upload CSV File
      </label>
      <div class="flex items-center">
        <label
          class="cursor-pointer px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          :class="{ 'opacity-50 cursor-not-allowed': isUploading }"
        >
          <span>{{ selectedFile ? selectedFile.name : 'Select File' }}</span>
          <input
            type="file"
            class="hidden"
            accept=".csv"
            @change="handleFileSelect"
            :disabled="isUploading"
          />
        </label>
        <button
          @click="downloadTemplate"
          class="ml-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          :disabled="isUploading"
        >
          Download Template
        </button>
      </div>
    </div>

    <div v-if="parseError" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm flex items-center">
      <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
      {{ parseError }}
    </div>

    <div v-if="parsedUsers.length > 0" class="mb-4">
      <h4 class="text-md font-medium mb-2">Preview ({{ parsedUsers.length }} users)</h4>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 bg-white rounded-md overflow-hidden">
          <thead class="bg-gray-100">
            <tr>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">First Name</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Last Name</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Email</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">User Role</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Account State</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="(user, index) in parsedUsers.slice(0, 5)" :key="index" class="hover:bg-gray-50">
              <td class="px-3 py-2 whitespace-nowrap text-xs">{{ user.firstName }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs">{{ user.lastName }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs">{{ user.email }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs">{{ user.role }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs">{{ user.status }}</td>
            </tr>
          </tbody>
        </table>
        <p v-if="parsedUsers.length > 5" class="text-xs text-gray-500 mt-2">
          Showing 5 of {{ parsedUsers.length }} users
        </p>
      </div>
    </div>

    <div class="flex justify-end">
      <button
        @click="uploadUsers"
        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 focus:outline-none"
        :disabled="!canUpload || isUploading"
        :class="{ 'opacity-50 cursor-not-allowed': !canUpload || isUploading }"
      >
        {{ isUploading ? 'Uploading...' : 'Upload Users' }}
      </button>
    </div>

    <div v-if="uploadResults.length > 0" class="mt-4">
      <h4 class="text-md font-medium mb-2">Upload Results</h4>
      <div class="bg-white p-4 rounded-md">
        <div v-for="(result, index) in uploadResults" :key="index" class="mb-2 text-sm flex items-center">
          <i :class="result.success ? 'fas fa-check-circle text-green-500 mr-2' : 'fas fa-times-circle text-red-500 mr-2'"></i>
          <span
            :class="result.success ? 'text-green-600' : 'text-red-600'"
          >
            {{ result.email }}: {{ result.message }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import Papa from 'papaparse';
import api from '@/services/apiService';

const props = defineProps({
  subsidiary: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['upload-complete']);

const selectedFile = ref(null);
const parsedUsers = ref([]);
const parseError = ref('');
const isUploading = ref(false);
const uploadResults = ref([]);

const canUpload = computed(() => {
  return selectedFile.value && parsedUsers.value.length > 0 && !parseError.value;
});

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  selectedFile.value = file;
  parseError.value = '';
  parsedUsers.value = [];
  uploadResults.value = [];

  Papa.parse(file, {
    header: true,
    skipEmptyLines: true,
    trimHeaders: true,
    complete: (results) => {
      console.log('Papa Parse results:', results);
      if (results.errors.length > 0) {
        console.error('Papa Parse errors:', results.errors);
        parseError.value = `CSV parsing error: ${results.errors[0].message}`;
        return;
      }

      // Check if we have data
      if (!results.data || results.data.length === 0) {
        parseError.value = 'No data found in CSV file';
        return;
      }

      // Check headers
      const firstRow = results.data[0];
      const headers = Object.keys(firstRow);
      console.log('CSV Headers found:', headers);

      const expectedHeaders = ['FirstName', 'LastName', 'Email', 'Password', 'UserRole', 'AccountState', 'Branch'];
      const missingHeaders = expectedHeaders.filter(h => !headers.includes(h));
      if (missingHeaders.length > 0) {
        parseError.value = `Missing required columns: ${missingHeaders.join(', ')}. Found columns: ${headers.join(', ')}`;
        return;
      }

      // Validate the data
      const requiredFields = ['FirstName', 'LastName', 'Email', 'Password', 'Branch'];
      const validRoles = ['systemAdmin', 'hr', 'it', 'supervisor', 'loanadmin'];
      const validStatuses = ['active', 'inactive'];

      const validatedUsers = [];
      let hasErrors = false;

      // Reset previous errors
      parseError.value = '';

      console.log('Starting validation with:', {
        requiredFields,
        validRoles,
        validStatuses,
        totalRows: results.data.length
      });

      results.data.forEach((row, index) => {
        // Debug: log the row data
        console.log(`Processing row ${index + 1}:`, row);

        // Skip empty rows
        if (!row || Object.keys(row).length === 0) {
          console.log(`Skipping empty row ${index + 1}`);
          return;
        }

        // Check required fields
        for (const field of requiredFields) {
          if (!row[field] || row[field].toString().trim() === '') {
            parseError.value = `Row ${index + 1}: Missing required field "${field}"`;
            hasErrors = true;
            return;
          }
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(row.Email)) {
          parseError.value = `Row ${index + 1}: Invalid email format "${row.Email}"`;
          hasErrors = true;
          return;
        }

        // Validate role (trim whitespace and check case-sensitively)
        const userRole = row.UserRole ? String(row.UserRole).trim() : '';
        console.log(`Row ${index + 1} - UserRole: "${userRole}", Valid roles:`, validRoles);
        console.log(`Row ${index + 1} - UserRole validation: includes=${validRoles.includes(userRole)}`);
        if (userRole && !validRoles.includes(userRole)) {
          console.error(`Row ${index + 1} - ROLE VALIDATION FAILED: "${userRole}" not in`, validRoles);
          parseError.value = `Row ${index + 1}: Invalid role "${userRole}". Must be one of: ${validRoles.join(', ')}`;
          hasErrors = true;
          return;
        }

        // Validate status if provided (trim whitespace and check case-sensitively)
        const accountState = row.AccountState ? String(row.AccountState).trim() : '';
        console.log(`Row ${index + 1} - AccountState: "${accountState}", Valid statuses:`, validStatuses);
        console.log(`Row ${index + 1} - AccountState validation: includes=${validStatuses.includes(accountState)}`);
        if (accountState && !validStatuses.includes(accountState)) {
          console.error(`Row ${index + 1} - STATUS VALIDATION FAILED: "${accountState}" not in`, validStatuses);
          parseError.value = `Row ${index + 1}: Invalid status "${accountState}". Must be one of: ${validStatuses.join(', ')}`;
          hasErrors = true;
          return;
        }

        // Validate branch is provided (required field)
        if (!row.Branch || row.Branch.trim() === '') {
          parseError.value = `Row ${index + 1}: Branch is required and cannot be empty`;
          hasErrors = true;
          return;
        }

        // Map the CSV columns to our internal format (trim all values and convert to string)
        const user = {
          firstName: row.FirstName ? String(row.FirstName).trim() : '',
          lastName: row.LastName ? String(row.LastName).trim() : '',
          email: row.Email ? String(row.Email).trim() : '',
          password: row.Password ? String(row.Password).trim() : '',
          role: userRole || 'loanadmin',
          status: accountState || 'active',
          branch: row.Branch ? String(row.Branch).trim() : ''
        };

        console.log(`Row ${index + 1} - Created user object:`, user);

        validatedUsers.push(user);
      });

      if (hasErrors) {
        console.error('Validation failed with error:', parseError.value);
        parsedUsers.value = [];
      } else {
        console.log('All users validated successfully:', validatedUsers);
        console.log('Total valid users:', validatedUsers.length);
        parsedUsers.value = validatedUsers;
        parseError.value = '';
      }
    }
  });
};

const downloadTemplate = () => {
  const csvContent = 'FirstName,LastName,Email,Password,UserRole,AccountState,Branch\nJohn,Doe,<EMAIL>,password123,loanadmin,active,Branch Name';
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'user_upload_template.csv';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

const uploadUsers = async () => {
  if (!canUpload.value) return;

  isUploading.value = true;
  uploadResults.value = [];

  try {
    // Process users one by one to handle individual errors
    for (const user of parsedUsers.value) {
      try {
        // Format the user data for the API
        const userData = {
          firstName: user.firstName,
          lastName: user.lastName,
          username: `${user.firstName};${user.lastName}`,
          email: user.email,
          password: user.password,
          role: user.role,
          status: user.status,
          branch: user.branch,
          sub: props.subsidiary,
          requirePasswordChange: true // Always require password change for new users
        };

        // Call the API to create the user
        await api.post('/user', userData, {
          headers: {
            'x-subsidiary': props.subsidiary,
            'Content-Type': 'application/json'
          }
        });

        uploadResults.value.push({
          email: user.email,
          success: true,
          message: 'Created successfully'
        });
      } catch (error) {
        uploadResults.value.push({
          email: user.email,
          success: false,
          message: error.response?.data?.message || 'Failed to create user'
        });
      }
    }

    // Emit event to refresh the user list
    emit('upload-complete');
  } catch (error) {
    console.error('Error uploading users:', error);
  } finally {
    isUploading.value = false;
  }
};
</script>
