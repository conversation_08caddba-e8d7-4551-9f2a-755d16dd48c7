<template>
  <div class="flex flex-col">
    <label :for="id" class="font-medium mb-1">
      {{ label }}
      <i v-if="disabled || readonly" class="fas fa-lock text-gray-400 text-xs ml-1" title="This field is locked based on your user assignment"></i>
    </label>
    <select
      :id="id"
      :value="modelValue"
      @change="$emit('update:modelValue', $event.target.value)"
      :class="[
        'border rounded p-2 w-full',
        disabled || readonly ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'
      ]"
      :disabled="disabled || readonly"
      required
    >
      <option v-for="option in options" :key="option" :value="option">
        {{ option }}
      </option>
    </select>
    <div v-if="disabled || readonly" class="text-xs text-gray-500 mt-1">
      <i class="fas fa-info-circle mr-1"></i>
      This field is pre-selected based on your user assignment and cannot be changed.
    </div>
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    label: String,
    options: Array,
    modelValue: String,
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
};
</script>
