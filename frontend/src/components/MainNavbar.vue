<template>
  <header
    :style="{ backgroundColor: theme.primaryColor }"
    class="fixed top-0 w-full z-50 shadow-sm"
  >
    <div class="max-w-7xl mx-auto px-4">
      <div class="flex h-16 items-center justify-between">
        <!-- Logo -->
        <h1 class="text-white text-xl font-bold truncate">
          {{ navbarTitle || "Digital User Access System" }}
        </h1>

        <!-- Desktop Menu -->
        <div class="hidden md:flex items-center space-x-4">
          <!-- Dashboard Button -->
          <Button
            variant="ghost"
            class="text-white hover:bg-white/10 hover:text-white"
            @click="navigateToDashboard"
          >
            <LayoutDashboard class="h-5 w-5 mr-2" />
            Dashboard
          </Button>

          <!-- User Management Button - Only visible to admin -->
          <Button
            v-if="hasUserManagementAccess"
            variant="ghost"
            class="text-white hover:bg-white/10 hover:text-white"
            @click="navigateToUserManagement"
          >
            <User class="h-5 w-5 mr-2" />
            User Management
          </Button>

          <!-- Tenant Management Button - Only visible to admin -->
          <!-- Commented out as requested
          <Button
            v-if="hasTenantManagementAccess"
            variant="ghost"
            class="text-white hover:bg-white/10 hover:text-white"
            @click="navigateToTenantManagement"
          >
            <Building class="h-5 w-5 mr-2" />
            Tenant Management
          </Button>
          -->

          <!-- User Dropdown -->
          <DropdownMenu v-if="currentUser">
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" class="text-white hover:bg-white/10 hover:text-white">
                <div class="flex items-center">
                  <div class="bg-white text-blue-600 h-8 w-8 rounded-full flex items-center justify-center font-bold mr-2">
                    {{ userInitials }}
                  </div>
                  <span>{{ formatUsername(currentUser.username) || currentUser.email }}</span>
                  <ChevronDown class="h-4 w-4 ml-2" />
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" class="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div class="px-2 py-1.5">
                <p class="text-sm font-medium text-muted-foreground">Logged in as</p>
                <p class="text-sm font-semibold">{{ formatUsername(currentUser.username) || currentUser.email }}</p>
                <p class="text-xs mt-1" :class="{'text-amber-600 font-medium': isWrongSubsidiary, 'text-muted-foreground': !isWrongSubsidiary}">
                  Subsidiary: {{ subsidiary }}
                  <span v-if="isWrongSubsidiary" class="block text-amber-600">
                    (You only have access to {{ userSubsidiary }})
                  </span>
                </p>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="showPasswordResetModal = true">
                <Key class="h-4 w-4 mr-2" />
                <span>Reset Password</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="handleLogout" class="text-red-600 focus:text-red-600">
                <LogOut class="h-4 w-4 mr-2" />
                <span>Logout</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden flex items-center space-x-4">
          <!-- Dashboard Button (Mobile) -->
          <Button
            variant="ghost"
            size="icon"
            class="text-white hover:bg-white/10 hover:text-white"
            @click="navigateToDashboard"
          >
            <LayoutDashboard class="h-5 w-5" />
          </Button>

          <!-- User Management Button (Mobile) - Only visible to admin -->
          <Button
            v-if="hasUserManagementAccess"
            variant="ghost"
            size="icon"
            class="text-white hover:bg-white/10 hover:text-white"
            @click="navigateToUserManagement"
          >
            <User class="h-5 w-5" />
          </Button>

          <!-- Tenant Management Button (Mobile) - Only visible to admin -->
          <!-- Commented out as requested
          <Button
            v-if="hasTenantManagementAccess"
            variant="ghost"
            size="icon"
            class="text-white hover:bg-white/10 hover:text-white"
            @click="navigateToTenantManagement"
          >
            <Building class="h-5 w-5" />
          </Button>
          -->

          <!-- User Dropdown (Mobile) -->
          <DropdownMenu v-if="currentUser">
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" size="icon" class="text-white hover:bg-white/10 hover:text-white p-0">
                <div class="bg-white text-blue-600 h-8 w-8 rounded-full flex items-center justify-center font-bold">
                  {{ userInitials }}
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" class="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div class="px-2 py-1.5">
                <p class="text-sm font-medium text-muted-foreground">Logged in as</p>
                <p class="text-sm font-semibold">{{ formatUsername(currentUser.username) || currentUser.email }}</p>
                <p class="text-xs mt-1" :class="{'text-amber-600 font-medium': isWrongSubsidiary, 'text-muted-foreground': !isWrongSubsidiary}">
                  Subsidiary: {{ subsidiary }}
                  <span v-if="isWrongSubsidiary" class="block text-amber-600">
                    (You only have access to {{ userSubsidiary }})
                  </span>
                </p>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="showPasswordResetModal = true">
                <Key class="h-4 w-4 mr-2" />
                <span>Reset Password</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="handleLogout" class="text-red-600 focus:text-red-600">
                <LogOut class="h-4 w-4 mr-2" />
                <span>Logout</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  </header>

  <!-- Password Reset Modal -->
  <div v-if="showPasswordResetModal" class="fixed inset-0 flex items-center justify-center z-50">
    <div class="fixed inset-0 bg-black bg-opacity-50" @click="showPasswordResetModal = false"></div>
    <div class="bg-white rounded shadow-md p-6 w-full max-w-md relative z-10">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium text-gray-800">
          Reset Your Password
        </h2>
        <button @click="showPasswordResetModal = false" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <PasswordReset />
    </div>
  </div>
</template>

<script setup>
import { subsidiaries } from "@/config/subsidiaries";
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { canAccess } from "@/utils/accessControl";
import PasswordReset from "@/components/PasswordReset.vue";

// Import shadcn-vue components
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Import navigation menu components
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";

// Import icons
import { LogOut, ChevronDown, LayoutDashboard, User, Key } from 'lucide-vue-next';
// Building icon is commented out as we're not using tenant management
// import { Building } from 'lucide-vue-next';

// Props
const props = defineProps({
  subsidiary: {
    type: String,
    required: true
  }
});

// Store and router
const store = useStore();
const router = useRouter();

// UI state
const showPasswordResetModal = ref(false);

// Computed properties
const currentUser = computed(() => store.getters['auth/currentUser']);

// Get the user's actual subsidiary
const userSubsidiary = computed(() => currentUser.value?.sub);

// Check if the user is trying to access a subsidiary they don't have access to
const isWrongSubsidiary = computed(() => {
  return userSubsidiary.value && props.subsidiary !== userSubsidiary.value;
});

const theme = computed(() => {
  return subsidiaries[props.subsidiary] || {
    name: "Digital User Access System",
    primaryColor: "#1f6fd0",
    secondaryColor: "#e7e7e7",
  };
});

const navbarTitle = computed(() => {
  const currentTheme = theme.value;
  const userRole = currentUser.value?.role;

  // Show role-specific titles for all subsidiaries
  if (userRole === 'hr' && currentTheme.hrName) {
    return currentTheme.hrName;
  } else if (userRole === 'loanadmin' && currentTheme.loanadminName) {
    return currentTheme.loanadminName;
  } else if ((userRole === 'it' || userRole === 'systemAdmin') && currentTheme.itName) {
    return currentTheme.itName;
  } else if (userRole === 'supervisor' && currentTheme.supervisorName) {
    return currentTheme.supervisorName;
  }

  return currentTheme.name;
});

// Access control computed properties
const hasUserManagementAccess = computed(() => {
  return canAccess('users', currentUser.value);
});

const hasTenantManagementAccess = computed(() => {
  return canAccess('tenants', currentUser.value);
});

const userInitials = computed(() => {
  if (!currentUser.value) return "";

  if (currentUser.value.username) {
    // Handle username with semicolon format (FirstName;LastName)
    if (currentUser.value.username.includes(';')) {
      const nameParts = currentUser.value.username.split(';');
      if (nameParts.length >= 1 && nameParts[0].trim()) {
        return nameParts[0].trim().charAt(0).toUpperCase();
      }
    }
    return currentUser.value.username.charAt(0).toUpperCase();
  } else if (currentUser.value.email) {
    return currentUser.value.email.charAt(0).toUpperCase();
  }

  return "U"; // Default fallback
});

// Methods
// Format username to display without semicolon
function formatUsername(username) {
  if (!username) return '';

  // If username contains a semicolon, replace it with a space
  if (username.includes(';')) {
    // Split by semicolon and join with space
    const parts = username.split(';').map(part => part.trim());
    return parts.join(' ');
  }

  return username;
}

function handleLogout() {
  console.log("Logging out...");
  store.dispatch('auth/logout');
}

function navigateToDashboard() {
  // Always navigate to the user's actual subsidiary if they have one
  const targetSubsidiary = userSubsidiary.value || props.subsidiary;
  router.push(`/dashboard/${targetSubsidiary}`);
}

function navigateToUserManagement() {
  // Always navigate to the user's actual subsidiary if they have one
  const targetSubsidiary = userSubsidiary.value || props.subsidiary;
  router.push(`/users/${targetSubsidiary}`);
}

function navigateToTenantManagement() {
  // Always navigate to the user's actual subsidiary if they have one
  const targetSubsidiary = userSubsidiary.value || props.subsidiary;
  router.push(`/tenants/${targetSubsidiary}`);
}
</script>

<style scoped>
/* Override shadcn-vue button styles for our theme */
:deep(.button-ghost) {
  color: white;
}

:deep(.button-ghost:hover) {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Override dropdown menu styles */
:deep(.dropdown-menu-content) {
  z-index: 50;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom styles for user avatar */
.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  height: 2rem;
  width: 2rem;
  background-color: white;
  color: #1f6fd0;
  font-weight: bold;
}
</style>
