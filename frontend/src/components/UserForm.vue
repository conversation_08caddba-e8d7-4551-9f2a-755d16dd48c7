<template>
  <form @submit.prevent="onSubmit">
    <!-- First row: First Name and Last Name -->
    <div class="grid grid-cols-2 gap-6 mb-4">
      <!-- First Name -->
      <div>
        <label class="block text-sm mb-2 text-gray-700 font-medium" for="firstName">
          First Name
        </label>
        <input
          id="firstName"
          v-model="firstName"
          type="text"
          class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
          required
          autocomplete="off"
        />
      </div>

      <!-- Last Name -->
      <div>
        <label class="block text-sm mb-2 text-gray-700 font-medium" for="lastName">
          Last Name
        </label>
        <input
          id="lastName"
          v-model="lastName"
          type="text"
          class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
          required
          autocomplete="off"
        />
      </div>
    </div>

    <!-- Second row: Email and Password -->
    <div class="grid grid-cols-2 gap-6 mb-4">
      <!-- Email -->
      <div>
        <label class="block text-sm mb-2 text-gray-700 font-medium" for="email">
          Email
        </label>
        <input
          id="email"
          v-model="email"
          type="email"
          class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
          required
          autocomplete="off"
        />
      </div>

      <!-- Password (only for new users) -->
      <div v-if="!isEditing">
        <label class="block text-sm mb-2 text-gray-700 font-medium" for="password">
          Password
        </label>
        <div class="relative">
          <input
            id="password"
            :value="password"
            @input="password = $event.target.value"
            :type="showPassword ? 'text' : 'password'"
            class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
            required
            autocomplete="new-password"
          />
          <button
            type="button"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
            @click="togglePasswordVisibility"
          >
            <i class="fas" :class="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Third row: Role and Account Status -->
    <div class="grid grid-cols-2 gap-6 mb-4">
      <!-- Role -->
      <div>
        <label class="block text-sm mb-2 text-gray-700 font-medium" for="role">
          User Role
        </label>
        <select
          id="role"
          v-model="role"
          class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm appearance-none bg-blue-50"
          required
          @change="handleRoleChange"
        >
          <option value="" disabled>Select role</option>
          <option value="systemAdmin">System Admin</option>
          <option value="hr">HR</option>
          <option value="it">IT</option>
          <option value="loanadmin">Loan Admin</option>
        </select>
        <div class="mt-1 text-xs text-gray-500">
          Selected role: {{ role || 'None' }}
        </div>
      </div>

      <!-- Account Status -->
      <div>
        <label class="block text-sm mb-2 text-gray-700 font-medium" for="status">
          Account State
        </label>
        <select
          id="status"
          v-model="status"
          class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm appearance-none bg-blue-50"
          required
        >
          <option value="" disabled>Select account state</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>
    </div>

    <!-- Fourth row: Branch -->
    <div class="mb-4">
      <div>
        <label class="block text-sm mb-2 text-gray-700 font-medium" for="branch">
          Branch
        </label>
        <select
          id="branch"
          v-model="branch"
          class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm appearance-none bg-blue-50"
          required
        >
          <option value="">Select branch</option>
          <option v-for="branchOption in branches" :key="branchOption" :value="branchOption">
            {{ branchOption }}
          </option>
        </select>
        <div class="mt-1 text-xs text-gray-500">
          <span class="text-red-500">*</span> Required. Assign user to a specific branch. When they create access requests, this branch will be pre-selected.
        </div>
      </div>
    </div>

    <!-- Fifth row: Require Password Change -->
    <div class="mb-4" v-if="!isEditing">
      <div class="flex items-center">
        <input
          id="requirePasswordChange"
          v-model="requirePasswordChange"
          type="checkbox"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="requirePasswordChange" class="ml-2 block text-sm text-gray-700">
          Require user to change password on first login
        </label>
      </div>
    </div>

    <!-- Form Error -->
    <div v-if="error" class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
      {{ error }}
    </div>

    <div class="flex justify-center w-full pt-6">
      <button
        type="submit"
        class="w-1/2 py-3 rounded bg-black text-white flex items-center justify-center text-base font-medium"
        :disabled="submitting"
      >
        <span v-if="submitting">
          <i class="fas fa-spinner fa-spin mr-2"></i> Saving...
        </span>
        <span v-else class="flex items-center justify-center">
          <i class="fas fa-user mr-2"></i>
          {{ isEditing ? 'Update User' : 'Create User' }}
        </span>
      </button>
    </div>
  </form>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { getBranches } from '../services/apiService';

const props = defineProps({
  user: {
    type: Object,
    default: null
  },
  error: {
    type: String,
    default: null
  },
  submitting: {
    type: Boolean,
    default: false
  }
});

// Log props for debugging
console.log('UserForm props:', props);

const emit = defineEmits(['submit']);

// Individual form fields instead of a single object
const firstName = ref('');
const lastName = ref('');
const email = ref('');
const password = ref('');
const role = ref('');
const status = ref('');
const branch = ref('');
const requirePasswordChange = ref(true); // Default to true for better security
const showPassword = ref(false);

// Data for dropdowns
const branches = ref([]);

// Computed property to check if we're editing
const isEditing = computed(() => !!props.user);

// Toggle password visibility
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// Handle role change
const handleRoleChange = (event) => {
  console.log('Role changed to:', event.target.value);
  console.log('Role ref value:', role.value);

  // Force the role to be the selected value
  role.value = event.target.value;

  // Update the DOM element directly to ensure it's set
  const roleSelect = document.getElementById('role');
  if (roleSelect) {
    roleSelect.value = event.target.value;
  }

  console.log('Role after change:', role.value);
};

// Handle form submission
const onSubmit = () => {
  // Validate form data
  if (!firstName.value || !lastName.value || !email.value || !role.value || !branch.value) {
    console.error('Form validation failed: Missing required fields');
    return;
  }

  if (!isEditing.value && !password.value) {
    console.error('Form validation failed: Password is required for new users');
    return;
  }

  // Log the role value before submission
  console.log('Selected role before form submission:', role.value);
  console.log('Role select element value:', document.getElementById('role')?.value);
  console.log('Selected branch before form submission:', branch.value);
  console.log('Branch select element value:', document.getElementById('branch')?.value);

  // Get the role directly from the select element
  const roleSelect = document.getElementById('role');
  const selectedRole = roleSelect ? roleSelect.value : role.value;

  // Get the branch directly from the select element
  const branchSelect = document.getElementById('branch');
  const selectedBranch = branchSelect ? branchSelect.value : branch.value;

  console.log('Role from select element:', selectedRole);
  console.log('Role from ref:', role.value);
  console.log('Branch from select element:', selectedBranch);
  console.log('Branch from ref:', branch.value);

  if (!selectedRole || selectedRole === '') {
    console.error('No role selected, this will cause issues!');
    return;
  }

  if (!selectedBranch || selectedBranch === '') {
    console.error('No branch selected, this will cause issues!');
    console.error('Branch validation failed - selectedBranch:', selectedBranch);
    console.error('Available branches:', branches.value);
    console.error('Branch select element:', branchSelect);
    console.error('Branch select options:', branchSelect ? Array.from(branchSelect.options).map(o => ({value: o.value, text: o.text})) : 'No select element');
    return;
  }

  // Create form data object with all required fields
  const formData = {
    firstName: firstName.value,
    lastName: lastName.value,
    username: `${firstName.value};${lastName.value}`,
    email: email.value,
    password: password.value,
    role: selectedRole, // Use the role from the select element
    status: status.value || 'active', // Default status if not selected
    branch: selectedBranch, // Use the branch from the select element (required)
    requirePasswordChange: requirePasswordChange.value // Include the requirePasswordChange flag
  };

  console.log('Form data being submitted:', formData);

  // Emit the form data to the parent component
  emit('submit', formData);
};

// Reset all form fields
const resetForm = () => {
  firstName.value = '';
  lastName.value = '';
  email.value = '';
  password.value = '';
  role.value = '';
  status.value = 'active'; // Default to active for new users
  branch.value = ''; // Ensure branch is empty - admin must select
  requirePasswordChange.value = true; // Reset to default (true)

  // Force clear DOM elements
  setTimeout(() => {
    document.querySelectorAll('input:not([type="checkbox"]), select').forEach(el => {
      if (el.tagName === 'INPUT') {
        el.value = '';
      } else if (el.tagName === 'SELECT') {
        // For select elements, set to empty value instead of first option
        el.value = '';
      }
    });

    // Specifically ensure branch select is empty and status is active
    const branchSelect = document.getElementById('branch');
    const statusSelect = document.getElementById('status');
    if (branchSelect) {
      branchSelect.value = '';
    }
    if (statusSelect) {
      statusSelect.value = 'active';
    }

    // Set checkbox to checked (default)
    const requirePasswordChangeCheckbox = document.getElementById('requirePasswordChange');
    if (requirePasswordChangeCheckbox) {
      requirePasswordChangeCheckbox.checked = true;
    }
  }, 0);
};

// Initialize form when user prop changes
watch(() => props.user, (newUser) => {
  console.log('User prop changed:', newUser);

  if (newUser) {
    // Extract first and last name from username if they're not already set
    let firstNameVal = newUser.firstName || '';
    let lastNameVal = newUser.lastName || '';

    if (!firstNameVal && !lastNameVal && newUser.username) {
      if (newUser.username.includes(';')) {
        const nameParts = newUser.username.split(';');
        firstNameVal = nameParts[0].trim();
        lastNameVal = nameParts.length > 1 ? nameParts[1].trim() : '';
      } else {
        // If username doesn't contain a semicolon, use it as the first name
        firstNameVal = newUser.username;
      }
    }

    console.log('Setting form values for editing:', {
      firstName: firstNameVal,
      lastName: lastNameVal,
      email: newUser.email,
      role: newUser.role,
      status: newUser.status,
      branch: newUser.branch
    });

    // Set form values for editing
    firstName.value = firstNameVal;
    lastName.value = lastNameVal;
    email.value = newUser.email || '';
    role.value = newUser.role || '';
    status.value = newUser.status || 'active';
    branch.value = newUser.branch || '';

    // Force update the DOM elements
    nextTick(() => {
      const firstNameInput = document.querySelector('input[id="firstName"]');
      const lastNameInput = document.querySelector('input[id="lastName"]');
      const emailInput = document.querySelector('input[id="email"]');
      const roleSelect = document.querySelector('select[id="role"]');
      const statusSelect = document.querySelector('select[id="status"]');
      const branchSelect = document.querySelector('select[id="branch"]');

      if (firstNameInput) firstNameInput.value = firstNameVal;
      if (lastNameInput) lastNameInput.value = lastNameVal;
      if (emailInput) emailInput.value = newUser.email || '';

      if (roleSelect) {
        // Find the option that matches the role
        const roleOption = Array.from(roleSelect.options).find(option =>
          option.value === newUser.role ||
          option.value.toLowerCase() === newUser.role?.toLowerCase()
        );

        if (roleOption) {
          roleSelect.value = roleOption.value;
        }
      }

      if (statusSelect) {
        // Find the option that matches the status
        const statusOption = Array.from(statusSelect.options).find(option =>
          option.value === newUser.status ||
          option.value.toLowerCase() === newUser.status?.toLowerCase()
        );

        if (statusOption) {
          statusSelect.value = statusOption.value;
        }
      }

      if (branchSelect && newUser.branch) {
        // Find the option that matches the branch
        const branchOption = Array.from(branchSelect.options).find(option =>
          option.value === newUser.branch
        );

        if (branchOption) {
          branchSelect.value = branchOption.value;
        }
      }
    });
  } else {
    // Reset form for new user
    resetForm();
  }
}, { immediate: true });

// Initialize form on mount
onMounted(() => {
  console.log('UserForm mounted with user:', props.user);

  if (props.user) {
    // If we have a user, set the form values
    const user = props.user;

    // Set the form values
    firstName.value = user.firstName || '';
    lastName.value = user.lastName || '';
    email.value = user.email || '';
    role.value = user.role || '';
    status.value = user.status || 'active';
    branch.value = user.branch || '';

    console.log('Form values set on mount:', {
      firstName: firstName.value,
      lastName: lastName.value,
      email: email.value,
      role: role.value,
      branch: branch.value,
      status: status.value
    });

    // Force update the DOM elements
    nextTick(() => {
      // Directly set form field values using DOM manipulation
      const firstNameInput = document.getElementById('firstName');
      const lastNameInput = document.getElementById('lastName');
      const emailInput = document.getElementById('email');
      const roleSelect = document.getElementById('role');
      const statusSelect = document.getElementById('status');

      if (firstNameInput) firstNameInput.value = user.firstName || '';
      if (lastNameInput) lastNameInput.value = user.lastName || '';
      if (emailInput) emailInput.value = user.email || '';

      if (roleSelect) roleSelect.value = user.role || '';
      if (statusSelect) statusSelect.value = user.status || 'active';

      console.log('Form fields set directly via DOM on mount');
    });
  } else {
    // Reset form fields for new user
    resetForm();
  }

  // Fetch branches for the dropdown
  fetchBranches();
});

// Fetch branches function
const fetchBranches = async () => {
  try {
    // Get current user's subsidiary from localStorage
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const subsidiary = user.sub || 'platinumkenya';

    console.log('Fetching branches for subsidiary:', subsidiary);
    const response = await getBranches(subsidiary);
    branches.value = response.map((b) => b.value || b);
    console.log('Branches fetched:', branches.value);
  } catch (error) {
    console.error('Error fetching branches:', error);
    branches.value = [];
  }
};
</script>
