export const subsidiaries = {
  platinumkenya: {
    name: "Platinum Kenya Credit IT Department", // Default for IT/admin users
    hrName: "Platinum Kenya Credit HR", // For HR users
    loanadminName: "Platinum Kenya Credit Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/platinum-credit-logo.png",
    primaryColor: "#002F6C",
    secondaryColor: "#E6E6E6",
  },
  premierkenya: {
    name: "Premier Kenya IT Department", // Default for IT/admin users
    hrName: "Premier Kenya HR", // For HR users
    loanadminName: "Premier Kenya Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/premier-credit-logo.png",
    primaryColor: "#F47920",
    secondaryColor: "#FFFFFF",
  },
  momentumcredit: {
    name: "Momentum Credit IT Department", // Default for IT/admin users
    hrName: "Momentum Credit HR", // For HR users
    loanadminName: "Momentum Credit Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/momentum-credit-logo.png",
    primaryColor: "#000000",
    secondaryColor: "#D4AF37",
  },
  platinumtanzania: {
    name: "Platinum Tanzania Credit IT Department", // Default for IT/admin users
    hrName: "Platinum Tanzania Credit HR", // For HR users
    loanadminName: "Platinum Tanzania Credit Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/platinum-credit-logo.png",
    primaryColor: "#002F6C",
    secondaryColor: "#E6E6E6",
  },
  premierfanikiwa: {
    name: "Premier Fanikiwa IT Department", // Default for IT/admin users
    hrName: "Premier Fanikiwa HR", // For HR users
    loanadminName: "Premier Fanikiwa Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/premier-credit-logo.png",
    primaryColor: "#F47920",
    secondaryColor: "#FFF5E1",
  },
  platinumuganda: {
    name: "Platinum Uganda IT Department", // Default for IT/admin users
    hrName: "Platinum Uganda HR", // For HR users
    loanadminName: "Platinum Uganda Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/platinum-credit-logo.png",
    primaryColor: "#002F6C",
    secondaryColor: "#E6E6E6",
  },
  premieruganda: {
    name: "Premier Uganda Customer Care", // Default name for non-IT users
    hrName: "Premier Uganda HR", // For HR users
    supervisorName: "Premier Uganda", // For supervisors (no department suffix)
    loanadminName: "Premier Uganda Customer Care", // For loan admin users
    itName: "Premier Uganda IT", // For IT/admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/premier-credit-logo.png",
    primaryColor: "#F47920",
    secondaryColor: "#FFFFFF",
  },
  spectrumzambia: {
    name: "Spectrum Zambia IT Department", // Default for IT/admin users
    hrName: "Spectrum Zambia HR", // For HR users
    loanadminName: "Spectrum Zambia Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/spectrum-credit-logo.png",
    primaryColor: "#002F6C",
    secondaryColor: "#E6E6E6",
  },
  premiersouthafrica: {
    name: "PREMIER CREDIT (PTY) LTD IT Department", // Default for IT/admin users
    hrName: "PREMIER CREDIT (PTY) LTD HR", // For HR users
    loanadminName: "PREMIER CREDIT (PTY) LTD Customer Care", // For loan admin users
    logo: "https://uat-uap.platcorpgroup.com/user-access/v1/static/premier-credit-logo.png",
    primaryColor: "#F47920",
    secondaryColor: "#FFFFFF",
  },

};

// URL to subsidiary mapping for auto-detection
export const urlToSubsidiaryMap = {
  // Production Mambu URLs
  'premierkenya.sandbox.mambu.com': 'premierkenya',
  'premieruganda.sandbox.mambu.com': 'premieruganda',
  'platinumkenya.sandbox.mambu.com': 'platinumkenya',
  'platinumuganda.sandbox.mambu.com': 'platinumuganda',
  'platinumtanzania.sandbox.mambu.com': 'platinumtanzania',
  'premierfanikiwa.sandbox.mambu.com': 'premierfanikiwa',
  'momentumcreditltd.sandbox.mambu.com': 'momentumcredit',
  'spectrumzambia.sandbox.mambu.com': 'spectrumzambia',
  'premiersouthafrica.sandbox.mambu.com': 'premiersouthafrica',

  // Production URLs (when available)
  'premierkenya.mambu.com': 'premierkenya',
  'premieruganda.mambu.com': 'premieruganda',
  'platinumkenya.mambu.com': 'platinumkenya',
  'platinumuganda.mambu.com': 'platinumuganda',
  'platinumtanzania.mambu.com': 'platinumtanzania',
  'premierfanikiwa.mambu.com': 'premierfanikiwa',
  'momentumcreditltd.mambu.com': 'momentumcredit',
  'spectrumzambia.mambu.com': 'spectrumzambia',
  'premiersouthafrica.mambu.com': 'premiersouthafrica',

  // Local development URLs (for testing)
  'premierkenya.local': 'premierkenya',
  'premieruganda.local': 'premieruganda',
  'platinumkenya.local': 'platinumkenya',
  'platinumuganda.local': 'platinumuganda',
  'platinumtanzania.local': 'platinumtanzania',
  'premierfanikiwa.local': 'premierfanikiwa',
  'momentumcreditltd.local': 'momentumcredit',
  'spectrumzambia.local': 'spectrumzambia',
  'premiersouthafrica.local': 'premiersouthafrica',

  // Localhost fallbacks
  'localhost': 'platinumkenya', // Default for localhost
  '127.0.0.1': 'platinumkenya', // Default for IP access
};

// Function to auto-detect subsidiary from current URL
export const detectSubsidiaryFromUrl = () => {
  // First check for environment variable override (for testing)
  if (process.env.VUE_APP_TEST_SUBSIDIARY && subsidiaries[process.env.VUE_APP_TEST_SUBSIDIARY]) {
    console.log(`Using subsidiary from environment variable: ${process.env.VUE_APP_TEST_SUBSIDIARY}`);
    return process.env.VUE_APP_TEST_SUBSIDIARY;
  }

  // Second check for URL parameter override (for development/testing)
  const urlParams = new URLSearchParams(window.location.search);
  const subsidiaryParam = urlParams.get('subsidiary');
  if (subsidiaryParam && subsidiaries[subsidiaryParam]) {
    console.log(`Using subsidiary from URL parameter: ${subsidiaryParam}`);
    return subsidiaryParam;
  }

  const hostname = window.location.hostname;

  // Check for exact hostname match first
  if (urlToSubsidiaryMap[hostname]) {
    console.log(`Using subsidiary from hostname mapping: ${urlToSubsidiaryMap[hostname]}`);
    return urlToSubsidiaryMap[hostname];
  }

  // Check for partial matches (in case of subdomains)
  for (const [urlPattern, subsidiary] of Object.entries(urlToSubsidiaryMap)) {
    if (hostname.includes(urlPattern.replace('.sandbox.mambu.com', '').replace('.mambu.com', '').replace('.local', ''))) {
      console.log(`Using subsidiary from partial hostname match: ${subsidiary}`);
      return subsidiary;
    }
  }

  // Default fallback
  console.log(`Using default subsidiary fallback: platinumkenya`);
  return 'platinumkenya';
};

// Function to check if user has access to detected subsidiary
export const validateSubsidiaryAccess = (userSubsidiary, detectedSubsidiary) => {
  // If user's subsidiary matches detected subsidiary, allow access
  if (userSubsidiary === detectedSubsidiary) {
    return true;
  }

  // System admins might have access to multiple subsidiaries
  // This can be expanded based on business rules
  return false;
};

