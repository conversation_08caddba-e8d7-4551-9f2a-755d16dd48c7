const develop = {
  BACKEND_SERVICE: "http://localhost:7080/user-access/v1",
};

const prod = {
  BACKEND_SERVICE: "https://digital-us.platcorpgroup.com/user-access/v1",
};

// New configuration for local development pointing to production backend
const dev_prod = {
  BACKEND_SERVICE: "https://digital-us.platcorpgroup.com/user-access/v1",
};

const choose = {
  develop,
  prod,
  dev_prod,
};

const config = process.env.VITE_APP_STAGE
  ? choose[process.env.VITE_APP_STAGE]
  : choose["prod"];

export default config;

