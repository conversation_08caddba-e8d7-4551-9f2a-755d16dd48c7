<template>
  <div
    class="p-8 min-h-screen"
    :style="{ backgroundColor: theme.secondaryColor}"
  >
    <MainSidebar :subsidiary="subsidiary" />
    <div class="flex-1 p-8 space-y-6">
      <div class="flex justify-between items-center mb-6 pt-4">
        <h1 class="text-3xl font-bold" :style="{ color: theme.primaryColor }">
          {{ dashboardTitle }}
        </h1>
        <div class="flex space-x-4">
          <button
            class="text-white px-6 py-2 rounded-lg shadow-md hover:opacity-90 transition ease-in-out"
            :style="{ backgroundColor: theme.primaryColor }"
          >
            <i class="fas fa-plus mr-2"></i> Add New Request
          </button>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-6">
        <div
          class="bg-white shadow-lg rounded-lg p-6 flex items-center justify-between hover:shadow-xl transition"
        >
          <i class="fas fa-users text-blue-600 text-5xl mr-4"></i>
          <div class="text-right">
            <h2 class="text-lg font-semibold text-gray-700">Total Users</h2>
            <p
              class="text-3xl font-bold"
              :style="{ color: theme.primaryColor }"
            >
              {{ totalUsers }}
            </p>
          </div>
        </div>
        <div
          class="bg-white shadow-lg rounded-lg p-6 flex items-center justify-between hover:shadow-xl transition"
        >
          <i class="fas fa-clock text-yellow-600 text-5xl mr-4"></i>
          <div class="text-right">
            <h2 class="text-lg font-semibold text-gray-700">
              Pending Requests
            </h2>
            <p class="text-3xl font-bold text-yellow-900">
              {{ pendingRequests }}
            </p>
          </div>
        </div>
        <div
          class="bg-white shadow-lg rounded-lg p-6 flex items-center justify-between hover:shadow-xl transition"
        >
          <i class="fas fa-check-circle text-green-600 text-5xl mr-4"></i>
          <div class="text-right">
            <h2 class="text-lg font-semibold text-gray-700">
              Approved Requests
            </h2>
            <p class="text-3xl font-bold text-green-900">
              {{ approvedRequests }}
            </p>
          </div>
        </div>
      </div>

      <!-- Recent Activity and Tracker -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition"
        >
          <h2
            class="text-xl font-semibold mb-4"
            :style="{ color: theme.primaryColor }"
          >
            Recent Activity
          </h2>
          <div v-if="loading" class="text-center text-xl text-gray-600">
            Loading...
          </div>
          <div v-if="error" class="text-center text-xl text-red-600">
            {{ error }}
          </div>
          <ul v-if="!loading && !error" class="space-y-4">
            <li
              v-for="(activity, index) in recentActivities"
              :key="index"
              class="border-b pb-2 flex items-center"
            >
              <i :class="getActivityIcon(activity)" class="mr-2"></i>
              <span>
                {{ activity.firstName }} {{ activity.lastName }}
                <span :class="getActivityClass(activity)">
                  {{ activity.approvalStatus || "Submitted" }}
                </span>
                a request on {{ formatDate(activity.createdAt) }}
              </span>
            </li>
            <li v-if="recentActivities.length === 0" class="text-center text-gray-500">
              No recent activities
            </li>
          </ul>
        </div>

        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition"
        >
          <h2
            class="text-xl font-semibold mb-4"
            :style="{ color: theme.primaryColor }"
          >
            Activity Tracker
          </h2>
          <div v-if="loading" class="text-center text-xl text-gray-600">
            Loading...
          </div>
          <div v-if="error" class="text-center text-xl text-red-600">
            {{ error }}
          </div>
          <div v-if="!loading && !error" class="space-y-6">
            <div class="flex justify-between items-center">
              <span>Processing Requests</span>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-yellow-600 h-2.5 rounded-full"
                  :style="{ width: processingWidth + '%' }"
                ></div>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Approved Requests</span>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-green-600 h-2.5 rounded-full"
                  :style="{ width: approvedWidth + '%' }"
                ></div>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Pending Requests</span>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-red-600 h-2.5 rounded-full"
                  :style="{ width: pendingWidth + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chart -->
      <div
        class="mt-8 bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition"
      >
        <h2
          class="text-xl font-semibold mb-4"
          :style="{ color: theme.primaryColor }"
        >
          User Activity Overview
        </h2>
        <canvas ref="dashboardChart"></canvas>
      </div>
    </div>



    <!-- Password Change Prompt -->
    <PasswordChangePrompt v-if="showPasswordChangePrompt" @password-changed="handlePasswordChanged" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import MainSidebar from "@/components/MainSidebar.vue";
import PasswordChangePrompt from "@/components/PasswordChangePrompt.vue";
import { subsidiaries } from "@/config/subsidiaries";
import Chart from "chart.js/auto";
import api from '@/services/apiService';

// Props
const props = defineProps(['subsidiary']);

// Store and router
const store = useStore();
const router = useRouter();

// State
const totalUsers = ref(0);
const pendingRequests = ref(0);
const approvedRequests = ref(0);
const loading = ref(true);
const error = ref(null);
const chartInstance = ref(null);
const recentActivities = ref([]);
const processingWidth = ref(0);
const approvedWidth = ref(0);
const pendingWidth = ref(0);
const showPasswordChangePrompt = ref(false);

// Computed properties
const theme = computed(() => {
  return subsidiaries[props.subsidiary] || subsidiaries["platinumkenya"];
});

const dashboardTitle = computed(() => {
  const currentUser = store.getters['auth/currentUser'];
  const userRole = currentUser?.role;
  const currentTheme = theme.value;

  let title = '';

  // Show role-specific titles for all subsidiaries
  if (userRole === 'hr' && currentTheme.hrName) {
    title = currentTheme.hrName;
  } else if (userRole === 'loanadmin' && currentTheme.loanadminName) {
    title = currentTheme.loanadminName;
  } else if ((userRole === 'it' || userRole === 'systemAdmin') && currentTheme.itName) {
    title = currentTheme.itName;
  } else {
    // Default fallback
    title = currentTheme.name;
  }

  // Add Dashboard suffix only if it doesn't already contain "Dashboard"
  if (!title.toLowerCase().includes('dashboard')) {
    title += ' Dashboard';
  }

  return title;
});

// Methods
const fetchDashboardData = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await api.get('/dashboard/dashboard-summary', {
      headers: { 'x-subsidiary': props.subsidiary }
    });

    const data = response.data;
    if (data) {
      totalUsers.value = data.totalUsers;
      pendingRequests.value = data.pendingRequests;
      approvedRequests.value = data.approvedRequests || 0;
    }
  } catch (err) {
    error.value = "Error fetching dashboard data.";
    console.error("Error fetching dashboard data:", err);
  } finally {
    loading.value = false;
    nextTick(() => {
      initChart();
    });
  }
};

const fetchRecentActivities = async () => {
  try {
    const response = await api.get('/dashboard/recent-activities', {
      headers: { 'x-subsidiary': props.subsidiary }
    });

    const data = response.data;
    recentActivities.value = [...(data.accessRequests || []), ...(data.approvals || [])];
  } catch (err) {
    error.value = "Error fetching recent activities.";
    console.error("Error fetching recent activities:", err);
  }
};

const fetchActivityTracker = async () => {
  try {
    const response = await api.get('/dashboard/activity-tracker', {
      headers: { 'x-subsidiary': props.subsidiary }
    });

    const data = response.data;
    if (data.totalRequests > 0) {
      processingWidth.value = (data.processingRequests / data.totalRequests) * 100;
      approvedWidth.value = (data.approvedRequests / data.totalRequests) * 100;
      pendingWidth.value = (data.pendingRequests / data.totalRequests) * 100;
    }
  } catch (err) {
    error.value = "Error fetching activity tracker data.";
    console.error("Error fetching activity tracker data:", err);
  }
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString();
};

const initChart = () => {
  const canvas = document.querySelector('canvas');
  if (!canvas) return;

  if (chartInstance.value) {
    chartInstance.value.destroy();
  }

  chartInstance.value = new Chart(canvas, {
    type: "bar",
    data: {
      labels: ["Users", "Pending Requests", "Approved Requests"],
      datasets: [
        {
          label: "User Metrics",
          data: [
            totalUsers.value,
            pendingRequests.value,
            approvedRequests.value,
          ],
          backgroundColor: ["#1E40AF", "#D97706", "#15803D"],
        },
      ],
    },
  });
};

const getActivityIcon = (activity) => {
  return activity.approvalStatus === "Approved"
    ? "fas fa-check-circle text-green-500"
    : activity.approvalStatus === "Rejected"
    ? "fas fa-times-circle text-red-500"
    : "fas fa-user-check text-blue-500";
};

const getActivityClass = (activity) => {
  return activity.approvalStatus === "Approved"
    ? "text-green-500"
    : activity.approvalStatus === "Rejected"
    ? "text-red-500"
    : "text-blue-500";
};

const handlePasswordChanged = async () => {
  showPasswordChangePrompt.value = false;

  // Remove the overlay if it exists
  const overlay = document.getElementById('password-change-overlay');
  if (overlay) {
    document.body.removeChild(overlay);
  }

  // Re-enable all interactive elements
  document.querySelectorAll('button, a, input, select').forEach(el => {
    el.removeAttribute('disabled');
    el.style.pointerEvents = '';
    el.style.opacity = '';
  });

  // Show a success notification
  const notification = document.createElement('div');
  notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-md shadow-lg z-50';
  notification.innerHTML = `
    <div class="flex items-center">
      <i class="fas fa-check-circle mr-2"></i>
      <span>Password changed successfully! Please log in with your new password.</span>
    </div>
  `;
  document.body.appendChild(notification);

  // Wait a moment before logging out
  setTimeout(async () => {
    // Log the user out
    await store.dispatch('auth/logout');

    // Redirect to login page
    router.push('/login');

    // Remove the notification after redirect
    setTimeout(() => {
      if (document.body.contains(notification)) {
        notification.classList.add('opacity-0', 'transition-opacity', 'duration-500');
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 500);
      }
    }, 500);
  }, 2000);
};

// Lifecycle hooks
onMounted(() => {
  // Get the current user's subsidiary from the store
  const currentUser = store.getters['auth/currentUser'];
  const userSubsidiary = currentUser?.sub;

  console.log('Dashboard mounted - User subsidiary:', userSubsidiary);
  console.log('Dashboard mounted - Route subsidiary:', props.subsidiary);

  // If the user's subsidiary doesn't match the route subsidiary, redirect
  if (userSubsidiary && props.subsidiary !== userSubsidiary) {
    console.warn(`Subsidiary mismatch: User belongs to ${userSubsidiary}, tried to access ${props.subsidiary}`);
    console.warn('Redirecting to correct subsidiary dashboard');

    // Redirect to the correct subsidiary dashboard
    router.replace(`/dashboard/${userSubsidiary}`);
    return;
  }

  // Check if the user needs to change their password
  if (currentUser?.requirePasswordChange) {
    console.log('User needs to change password');
    showPasswordChangePrompt.value = true;

    // Disable all interactive elements on the dashboard until password is changed
    document.querySelectorAll('button, a, input, select').forEach(el => {
      if (!el.closest('.password-change-prompt')) {
        el.setAttribute('disabled', 'disabled');
        el.style.pointerEvents = 'none';
        el.style.opacity = '0.5';
      }
    });

    // Add a semi-transparent overlay to prevent interaction with the dashboard
    const overlay = document.createElement('div');
    overlay.id = 'password-change-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '40';
    document.body.appendChild(overlay);
  }

  // Fetch data only if we're on the correct subsidiary
  fetchDashboardData();
  fetchRecentActivities();
  fetchActivityTracker();
});
</script>

<style scoped>
@import "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";
</style>
