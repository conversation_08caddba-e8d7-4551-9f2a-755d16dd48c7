<template>
  <div class="form-container">
    <!-- Header -->
    <div
      class="header-container border-b pb-4 mb-6 shadow-md bg-white p-6 rounded-lg flex items-center"
    >
      <div class="logo-container mr-6">
        <img :src="theme.logo" alt="Subsidiary Logo" class="logo h-20" />
      </div>
      <h2
        class="form-title text-2xl font-bold text-gray-800 flex-1 text-center"
      >
        User Access Revocation
      </h2>
    </div>

    <!-- Mode Toggle -->
    <div class="mb-6 flex justify-center space-x-4">
      <button
        @click="mode = 'single'"
        :class="mode === 'single' ? 'bg-blue-600 text-white' : 'bg-gray-200'"
        class="px-4 py-2 rounded shadow"
      >
        Single Form
      </button>
      <button
        @click="mode = 'bulk'"
        :class="mode === 'bulk' ? 'bg-blue-600 text-white' : 'bg-gray-200'"
        class="px-4 py-2 rounded shadow"
      >
        Bulk Upload
      </button>
    </div>

    <!-- Snackbar -->
    <transition name="fade">
      <div
        v-if="snackbar.show"
        :class="[
          'snackbar',
          snackbar.type === 'success' ? 'snackbar-success' : 'snackbar-error',
        ]"
      >
        {{ snackbar.message }}
      </div>
    </transition>

    <!-- SINGLE FORM MODE -->
    <form
      v-if="mode === 'single'"
      @submit.prevent="submitForm"
      class="bg-white p-6 shadow-lg rounded-lg"
    >
      <!-- Employee Information Section -->
      <div class="section mb-6">
        <h3 class="section-title">Employee Information</h3>
        <div class="grid grid-cols-2 gap-6">
          <div class="form-group">
            <label>Full Name</label>
            <input
              v-model="form.fullName"
              type="text"
              required
              class="input-field"
            />
          </div>
          <div class="form-group">
            <label>Employee ID</label>
            <input
              v-model="form.employeeId"
              type="text"
              required
              class="input-field"
            />
          </div>
          <DropdownField
            label="Job Title"
            v-model="form.jobTitle"
            :options="roles"
          />
          <DropdownField
            label="Department"
            v-model="form.department"
            :options="departments"
          />
          <div v-if="subsidiary !== 'premieruganda'" class="form-group">
            <label>Contact Information</label>
            <input
              v-model="form.contact"
              type="text"
              :required="subsidiary !== 'premieruganda'"
              class="input-field"
            />
          </div>
        </div>
      </div>

      <!-- Access Details Section -->
      <div class="section mb-6">
        <h3 class="section-title">Access Details</h3>
        <div>
          <label class="font-semibold block mb-2"
            >Systems to Revoke Access From</label
          >
          <div class="relative border rounded p-2 bg-white">
            <div
              class="cursor-pointer"
              @click="showSystemDropdown = !showSystemDropdown"
            >
              <span v-if="form.systems.length">
                {{
                  form.systems.includes("ALL")
                    ? "All Systems"
                    : form.systems.join(", ")
                }}
              </span>
              <span v-else class="text-gray-400">Select system(s)</span>
            </div>
            <div
              v-if="showSystemDropdown"
              class="absolute z-10 bg-white border rounded mt-1 shadow max-h-48 overflow-y-auto w-full"
            >
              <div class="p-2">
                <label class="block">
                  <input
                    type="checkbox"
                    value="ALL"
                    v-model="form.systems"
                    @change="toggleAllSystems"
                    class="mr-2"
                  />
                  All
                </label>
                <label
                  v-for="system in systemsList"
                  :key="system"
                  class="block mt-1"
                >
                  <input
                    type="checkbox"
                    :value="system"
                    v-model="form.systems"
                    class="mr-2"
                    :disabled="form.systems.includes('ALL')"
                  />
                  {{ system }}
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Reason for Revocation -->
      <div class="section">
        <h3>Reason for Revocation</h3>
        <div class="form-group">
          <label
            ><input
              type="radio"
              v-model="form.reason"
              value="Resignation/Termination"
            />
            Resignation/Termination</label
          >
          <label
            ><input
              type="radio"
              v-model="form.reason"
              value="Role Change/Transfer"
            />
            Role Change/Transfer</label
          >
          <label
            ><input
              type="radio"
              v-model="form.reason"
              value="Security Violation"
            />
            Security Violation</label
          >
          <label
            ><input type="radio" v-model="form.reason" value="Other" /> Other
            (please specify)</label
          >
          <input
            v-if="form.reason === 'Other'"
            v-model="form.otherReason"
            type="text"
            placeholder="Specify reason"
          />
        </div>
      </div>

      <!-- Approver Details Section -->
      <div class="section mb-6">
        <h3 class="section-title">Approver Details</h3>
        <div class="grid grid-cols-2 gap-6">
          <div class="form-group">
            <label>Approver Name</label>
            <input
              v-model="form.approverName"
              type="text"
              required
              class="input-field"
            />
          </div>
          <div class="form-group">
            <label>Approver Job Title</label>
            <input
              v-model="form.approverJobTitle"
              type="text"
              required
              class="input-field"
            />
          </div>
          <div v-if="subsidiary !== 'premieruganda'" class="form-group">
            <label>Approver Contact</label>
            <input
              v-model="form.approverContact"
              type="text"
              :required="subsidiary !== 'premieruganda'"
              class="input-field"
            />
          </div>
        </div>
      </div>

      <!-- Signature - Hidden for Premier Uganda -->
      <div v-if="subsidiary !== 'premieruganda'" class="section mb-6">
        <h3 class="section-title text-lg font-semibold text-gray-700 mb-4">Approval Signature</h3>
        <p class="text-sm text-gray-600 mb-4">Please upload your signature to approve this request:</p>

        <!-- Custom Signature Upload Area -->
        <div class="signature-upload-container">
          <label
            for="signature-input"
            class="signature-upload-area"
            :class="{ 'has-signature': form.signature }"
          >
            <!-- Upload Icon and Text (shown when no signature) -->
            <div v-if="!form.signature" class="upload-placeholder">
              <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <div class="upload-text">
                <p class="upload-main-text">Click to upload signature</p>
                <p class="upload-sub-text">PNG, JPG, GIF up to 10MB</p>
              </div>
            </div>

            <!-- Signature Preview (shown when signature is uploaded) -->
            <div v-if="form.signature" class="signature-preview-container">
              <img
                :src="form.signature"
                class="signature-preview-image"
                alt="Uploaded signature"
              />
              <div class="signature-overlay">
                <svg class="edit-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                <span class="edit-text">Click to change</span>
              </div>
            </div>
          </label>

          <input
            id="signature-input"
            type="file"
            @change="handleSignatureUpload"
            accept="image/*"
            class="hidden"
          />
        </div>

        <!-- File Info -->
        <div v-if="form.signatureFile" class="mt-3 text-sm text-gray-600">
          <p>Selected: {{ form.signatureFile.name }}</p>
          <p>Size: {{ formatFileSize(form.signatureFile.size) }}</p>
        </div>
      </div>

      <!-- Submit -->
      <div class="text-center">
        <button
          type="submit"
          class="submit-btn"
          :style="{ backgroundColor: theme.primaryColor }"
        >
          Submit Revocation Request
        </button>
      </div>
    </form>

    <!-- BULK UPLOAD MODE -->
    <div v-else class="bg-white p-6 shadow-lg rounded-lg text-center">
      <h3 class="text-lg font-semibold mb-6">
        Upload Excel File (.xlsx or .xls)
      </h3>

      <!-- Custom File Upload Button -->
      <div class="mb-6">
        <label
          for="bulk-file-input"
          class="cursor-pointer inline-flex items-center px-6 py-3 border-2 border-dashed border-gray-300 rounded-lg text-sm font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
          :class="{ 'border-blue-400 bg-blue-50 text-blue-600': bulkFile }"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          {{ bulkFile ? bulkFile.name : 'Click to select file' }}
        </label>
        <input
          id="bulk-file-input"
          type="file"
          accept=".xlsx,.xls"
          @change="handleBulkFileUpload"
          class="hidden"
        />
      </div>

      <!-- File Info -->
      <div v-if="bulkFile" class="mb-4 text-sm text-gray-600">
        <p>Selected: {{ bulkFile.name }}</p>
        <p>Size: {{ formatFileSize(bulkFile.size) }}</p>
      </div>

      <button
        :disabled="!bulkFile || loading"
        @click="submitBulkUpload"
        class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
      >
        {{ loading ? 'Uploading...' : 'Submit to IT' }}
      </button>
    </div>
  </div>
</template>

<script>
import { subsidiaries } from "@/config/subsidiaries";
import DropdownField from "@/components/DropdownField.vue";
import {
  getRoles,
  getDepartments,
  submitRevocationForm,
  submitRevocationBulkUpload,
} from "@/services/apiService";

export default {
  props: ["subsidiary"],
  components: { DropdownField },
  data() {
    return {
      mode: "single",
      roles: [],
      departments: [],
      loading: false,
      showSystemDropdown: false,
      form: {
        fullName: "",
        employeeId: "",
        jobTitle: "",
        department: "",
        contact: "",
        systems: [],
        reason: "",
        otherReason: "",
        approverName: "",
        approverJobTitle: "",
        approverContact: "",
        signatureFile: null,
        signature: null,
      },
      systemsList: [
        "Mambu System",
        "Yeaster System",
        "HR System",
        "Finance System",
        "IT System",
        "CRM System",
      ],
      bulkFile: null,
      snackbar: {
        show: false,
        message: "",
        type: "success",
      },
    };
  },
  computed: {
    theme() {
      return subsidiaries[this.subsidiary] || subsidiaries["platinumKenya"];
    },
  },
  mounted() {
    this.fetchRoles();
    this.fetchDepartments();
  },
  methods: {
    async fetchRoles() {
      const res = await getRoles(this.subsidiary);
      this.roles = res?.map((r) => r.value) || [];
    },
    async fetchDepartments() {
      const res = await getDepartments(this.subsidiary);
      this.departments = res?.map((d) => d.key) || [];
    },

    async submitForm() {
      const formData = new FormData();
      for (const key in this.form) {
        if (key === "signatureFile") {
          formData.append("signature", this.form.signatureFile);
        } else if (key === "systems") {
          formData.append("systems", JSON.stringify(this.form.systems));
        } else {
          formData.append(key, this.form[key]);
        }
      }
      formData.append("subsidiary", this.subsidiary);

      try {
        await submitRevocationForm(formData);
        this.showSnackbar("Revocation submitted successfully", "success");
        this.resetForm();
      } catch (err) {
        this.showSnackbar("Failed: " + err.message, "error");
      }
    },

    resetForm() {
      Object.assign(this.form, {
        fullName: "",
        employeeId: "",
        jobTitle: "",
        department: "",
        contact: "",
        systems: [],
        reason: "",
        otherReason: "",
        approverName: "",
        approverJobTitle: "",
        approverContact: "",
        signatureFile: null,
        signature: null,
      });

      // Reset file input
      const signatureInput = document.getElementById('signature-input');
      if (signatureInput) signatureInput.value = '';
    },

    handleSignatureUpload(event) {
      const file = event.target.files[0];
      if (file) {
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
          this.showSnackbar('Please select a valid image file (PNG, JPG, GIF)', 'error');
          event.target.value = ''; // Reset file input
          return;
        }

        // Validate file size (10MB max)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
          this.showSnackbar('File size must be less than 10MB', 'error');
          event.target.value = ''; // Reset file input
          return;
        }

        this.form.signatureFile = file;
        const reader = new FileReader();
        reader.onload = (e) => (this.form.signature = e.target.result);
        reader.readAsDataURL(file);
      }
    },

    toggleAllSystems() {
      if (this.form.systems.includes("ALL")) {
        this.form.systems = ["ALL"];
      }
    },

    handleBulkFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        // Validate file type
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
          'application/vnd.ms-excel' // .xls
        ];

        if (!allowedTypes.includes(file.type)) {
          this.showSnackbar('Please select a valid Excel file (.xlsx or .xls)', 'error');
          event.target.value = ''; // Reset file input
          return;
        }

        // Validate file size (10MB max)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
          this.showSnackbar('File size must be less than 10MB', 'error');
          event.target.value = ''; // Reset file input
          return;
        }

        this.bulkFile = file;
      }
    },

    async submitBulkUpload() {
      this.loading = true;
      const formData = new FormData();
      formData.append("file", this.bulkFile);
      formData.append("subsidiary", this.subsidiary);

      try {
        console.log(`Submitting bulk upload for subsidiary: ${this.subsidiary}`);
        const res = await submitRevocationBulkUpload(formData, this.subsidiary);
        console.log('Bulk upload response:', res);
        this.showSnackbar(res.message || "Upload successful", "success");
        this.bulkFile = null;
        // Reset file input
        const fileInput = document.getElementById('bulk-file-input');
        if (fileInput) fileInput.value = '';
      } catch (error) {
        console.error('Bulk upload error:', error);
        this.showSnackbar(`Bulk upload failed: ${error.message || 'Unknown error'}`, "error");
      } finally {
        this.loading = false;
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    showSnackbar(message, type = "success") {
      this.snackbar.message = message;
      this.snackbar.type = type;
      this.snackbar.show = true;
      setTimeout(() => (this.snackbar.show = false), 4000);
    },
  },
};
</script>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  margin-bottom: 20px;
  padding-top: 20px;
}
.logo-container {
  display: flex;
  justify-content: center;
}
.logo {
  height: 80px;
}
.form-container {
  /* max-width: 1080px; */
  /* margin: 50px auto; */
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.section {
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 15px;
}
.section h3 {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 10px;
}
.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}
.form-group input,
.form-group select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.snackbar {
  position: fixed;
  top: 20px;
  right: 30px;
  padding: 12px 20px;
  border-radius: 6px;
  color: white;
  z-index: 1000;
  font-weight: 600;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.snackbar-success {
  background-color: #38a169;
}
.snackbar-error {
  background-color: #e53e3e;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.submit-btn {
  width: 100%;
  padding: 10px;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
}
.submit-btn:hover {
  opacity: 0.9;
}

/* Signature Upload Styles */
.signature-upload-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.signature-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  background-color: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.signature-upload-area:hover {
  border-color: #9ca3af;
  background-color: #f3f4f6;
}

.signature-upload-area.has-signature {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
}

.upload-icon {
  width: 48px;
  height: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.upload-text {
  color: #6b7280;
}

.upload-main-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #374151;
}

.upload-sub-text {
  font-size: 14px;
  color: #9ca3af;
}

.signature-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signature-preview-image {
  max-width: 100%;
  max-height: 180px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.signature-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.signature-upload-area:hover .signature-overlay {
  opacity: 1;
}

.edit-icon {
  width: 24px;
  height: 24px;
  color: white;
  margin-bottom: 8px;
}

.edit-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}
</style>
