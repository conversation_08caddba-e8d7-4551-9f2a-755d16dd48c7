<template>
  <div class="p-20 bg-white shadow-lg rounded-xl">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-2xl font-bold" :style="{ color: theme.primaryColor }">
        Pending Access Requests
      </h2>
      <input
        v-model="search"
        placeholder="Search requests..."
        class="border p-2 rounded w-1/3 shadow-sm focus:outline-none focus:ring"
        :style="{
          borderColor: theme.primaryColor,
          boxShadow: `0px 0px 4px ${theme.primaryColor}`,
        }"
      />
    </div>

    <div class="overflow-x-auto rounded-lg shadow-sm">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead :style="{ backgroundColor: theme.primaryColor, color: 'white' }">
          <tr>
            <th class="p-3 border">ID</th>
            <th class="p-3 border">Access Requestor</th>
            <th v-if="subsidiary !== 'premieruganda'" class="p-3 border">Department</th>
            <th class="p-3 border">Submitted Date</th>
            <th class="p-3 border">Status</th>
            <th class="p-3 border">Rejection Reason</th>
            <th class="p-3 border text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="request in paginatedRequests"
            :key="request.id"
            class="hover:bg-gray-100 transition"
          >
            <td class="p-3 border">{{ request.id }}</td>
            <td class="p-3 border">
              {{ request.firstName }} {{ request.lastName }}
            </td>
            <td v-if="subsidiary !== 'premieruganda'" class="p-3 border">{{ request.department }}</td>
            <td class="p-3 border text-sm">{{ formatDate(request.createdAt) }}</td>
            <td
              class="p-3 border font-semibold"
              :class="statusClass(request.approvalStatus)"
            >
              {{ request.approvalStatus }}
            </td>
            <td class="p-3 border text-sm">
              <span v-if="request.approvalStatus === 'Rejected' && request.rejectionReason"
                    class="text-red-600 italic">
                {{ request.rejectionReason }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </td>
            <td class="p-3 border flex justify-center space-x-2">
              <button
                v-if="canApprove(request.approvalStatus)"
                @click="openApprovalModal(request)"
                class="bg-gradient-to-r from-green-400 to-green-600 text-white px-4 py-2 rounded shadow hover:scale-105 transition"
              >
                ✓ Approve
              </button>
              <button
                v-if="canReject(request.approvalStatus)"
                @click="openRejectionModal(request)"
                class="bg-gradient-to-r from-red-400 to-red-600 text-white px-4 py-2 rounded shadow hover:scale-105 transition"
              >
                ✗ Reject
              </button>
              <button
                v-if="canEdit(request.approvalStatus)"
                @click="openEditModal(request)"
                class="bg-gradient-to-r from-blue-400 to-blue-600 text-white px-4 py-2 rounded shadow hover:scale-105 transition"
              >
                ✏️ Edit
              </button>

            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination Controls -->
    <div class="flex justify-between items-center mt-4">
      <label class="text-sm">Items per page:</label>
      <select v-model="itemsPerPage" class="border p-2 rounded">
        <option
          v-for="option in paginationOptions"
          :key="option"
          :value="option"
        >
          {{ option }}
        </option>
      </select>
      <div>
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
          class="px-4 py-2 mx-1 rounded bg-gray-300 hover:bg-gray-400 disabled:opacity-50"
          :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
        >
          ◀ Previous
        </button>
        <span class="text-gray-700">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="px-4 py-2 mx-1 rounded bg-gray-300 hover:bg-gray-400 disabled:opacity-50"
          :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
        >
          Next ▶
        </button>
      </div>
    </div>

    <!-- Approval Modal with transparent backdrop -->
    <div
      v-if="showApprovalModal"
      class="fixed inset-0 flex items-center justify-center bg-transparent backdrop-blur-sm z-50"
    >
      <div class="bg-white/95 p-6 rounded-lg shadow-xl w-[600px] max-h-[90vh] overflow-y-auto">
        <h3 class="text-xl font-semibold mb-2 text-gray-800 flex items-center">
          <span :style="{ color: theme.primaryColor }">Approve Access Request</span>
          <div class="ml-auto">
            <button @click="closeApprovalModal" class="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </h3>

        <!-- Approval Flow Progress Indicator -->
        <div class="mb-6 mt-4">
          <!-- Premier Kenya Workflow (with Branch Head) -->
          <div v-if="subsidiary === 'premierkenya'" class="flex items-center justify-between">
            <!-- Submitted Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center bg-green-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="text-xs mt-1 font-medium">Submitted</span>
            </div>

            <!-- Progress Line 1 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(1) }"></div>

            <!-- Branch Head Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(1)">
                <span class="text-xs font-bold">BH</span>
              </div>
              <span class="text-xs mt-1 font-medium text-center">{{ getBranchHeadTitle() }}</span>
            </div>

            <!-- Progress Line 2 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(2) }"></div>

            <!-- HR Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(2)">
                <span class="text-sm font-bold">HR</span>
              </div>
              <span class="text-xs mt-1 font-medium">HR Approval</span>
            </div>

            <!-- Progress Line 3 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(3) }"></div>

            <!-- IT Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(3)">
                <span class="text-sm font-bold">IT</span>
              </div>
              <span class="text-xs mt-1 font-medium">IT Approval</span>
            </div>
          </div>

          <!-- Other Subsidiaries Workflow (HR -> IT) -->
          <div v-else class="flex items-center justify-between">
            <!-- Submitted Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center bg-green-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="text-xs mt-1 font-medium">Submitted</span>
            </div>

            <!-- Progress Line 1 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(1) }"></div>

            <!-- HR Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(1)">
                <span class="text-sm font-bold">HR</span>
              </div>
              <span class="text-xs mt-1 font-medium">HR Approval</span>
            </div>

            <!-- Progress Line 2 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(2, true) }"></div>

            <!-- IT Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(2)">
                <span class="text-sm font-bold">IT</span>
              </div>
              <span class="text-xs mt-1 font-medium">IT Approval</span>
            </div>
          </div>
        </div>

        <p class="text-gray-600 mb-4">
          Review the request details before approving.
        </p>

        <!-- Displaying Full Request Details -->
        <div class="bg-gray-50 p-4 rounded-lg mb-4 border border-gray-200 shadow-sm">
          <h4 class="text-lg font-semibold mb-4" :style="{ color: theme.primaryColor }">Complete Request Details</h4>

          <!-- Personal Information Section -->
          <div class="mb-6">
            <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Personal Information</h5>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">First Name</p>
                <p class="font-medium">{{ selectedRequest?.firstName }}</p>
              </div>
              <div v-if="selectedRequest?.middleName" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Middle Name</p>
                <p class="font-medium">{{ selectedRequest?.middleName }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Last Name</p>
                <p class="font-medium">{{ selectedRequest?.lastName }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Email</p>
                <p class="font-medium">{{ selectedRequest?.email || 'N/A' }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Telephone No</p>
                <p class="font-medium">{{ selectedRequest?.telephone }}</p>
              </div>
              <!-- Premier Uganda specific fields -->
              <div v-if="subsidiary === 'premieruganda' && selectedRequest?.nin" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">NIN</p>
                <p class="font-medium">{{ selectedRequest?.nin }}</p>
              </div>
              <div v-if="subsidiary === 'premieruganda' && selectedRequest?.dateOfBirth" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Date of Birth</p>
                <p class="font-medium">{{ selectedRequest?.dateOfBirth }}</p>
              </div>
              <div v-if="selectedRequest?.tin" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">TIN</p>
                <p class="font-medium">{{ selectedRequest?.tin }}</p>
              </div>
              <div v-if="selectedRequest?.nssf" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">NSSF</p>
                <p class="font-medium">{{ selectedRequest?.nssf }}</p>
              </div>
            </div>
          </div>

          <!-- Next of Kin Information (Premier Uganda) -->
          <div v-if="subsidiary === 'premieruganda' && (selectedRequest?.nextOfKinName || selectedRequest?.nextOfKinMobile)" class="mb-6">
            <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Next of Kin Information</h5>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div v-if="selectedRequest?.nextOfKinName" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Next of Kin Name</p>
                <p class="font-medium">{{ selectedRequest?.nextOfKinName }}</p>
              </div>
              <div v-if="selectedRequest?.nextOfKinMobile" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Next of Kin Mobile</p>
                <p class="font-medium">{{ selectedRequest?.nextOfKinMobile }}</p>
              </div>
              <div v-if="selectedRequest?.nextOfKinRelationship" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Relationship</p>
                <p class="font-medium">{{ selectedRequest?.nextOfKinRelationship }}</p>
              </div>
              <div v-if="selectedRequest?.nextOfKinDependents" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Number of Dependents</p>
                <p class="font-medium">{{ selectedRequest?.nextOfKinDependents }}</p>
              </div>
            </div>
          </div>

          <!-- System Access Information -->
          <div class="mb-6">
            <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">System Access Information</h5>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">System Name</p>
                <p class="font-medium">{{ formatSystemName(selectedRequest?.systemName) }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Branch</p>
                <p class="font-medium">{{ selectedRequest?.branch }}</p>
              </div>
              <div v-if="subsidiary !== 'premieruganda'" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Department</p>
                <p class="font-medium">{{ selectedRequest?.department }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Access Type</p>
                <p class="font-medium">{{ selectedRequest?.accessType }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Role to be Assigned</p>
                <p class="font-medium">{{ selectedRequest?.role }}</p>
              </div>
              <div v-if="selectedRequest?.previousRole" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Previous Role</p>
                <p class="font-medium">{{ selectedRequest?.previousRole }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100 col-span-2">
                <p class="text-gray-500 mb-1">Reason for Access</p>
                <p class="font-medium">{{ selectedRequest?.reason || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Supporting Documents -->
          <div v-if="selectedRequest?.attachments && selectedRequest?.attachments.length > 0" class="mb-6">
            <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Supporting Documents</h5>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <div class="grid grid-cols-1 gap-2">
                <div v-for="(attachment, index) in selectedRequest?.attachments" :key="index" class="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span class="text-sm font-medium">{{ attachment.filename || `Document ${index + 1}` }}</span>
                  </div>
                  <span class="text-xs text-gray-500">{{ attachment.contentType || 'Unknown type' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Request Status Information -->
          <div class="mb-4">
            <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Request Status</h5>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Submitted Date</p>
                <p class="font-medium">{{ formatDate(selectedRequest?.createdAt) }}</p>
              </div>
              <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
                <p class="text-gray-500 mb-1">Current Status</p>
                <p class="font-medium" :class="statusClass(selectedRequest?.approvalStatus)">{{ selectedRequest?.approvalStatus }}</p>
              </div>
            </div>
          </div>

          <!-- HR Section for Premier Uganda -->
          <div v-if="subsidiary === 'premieruganda' && selectedRequest" class="mt-6 p-4 border rounded-md shadow-sm bg-blue-50">
            <h4 class="font-semibold mb-4 text-lg text-blue-800">Additional Information to Be Completed by HR</h4>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Staff ID Number</label>
                <input
                  type="text"
                  v-model="hrFields.staffId"
                  placeholder="Filled in by HR before sending to MIS"
                  class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Position</label>
                <input
                  type="text"
                  v-model="hrFields.position"
                  placeholder="Free text field filled by HR"
                  class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <select
                  v-model="hrFields.department"
                  class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select Department</option>
                  <option v-for="dept in departments" :key="dept" :value="dept">{{ dept }}</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input
                  type="date"
                  v-model="hrFields.startDate"
                  class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
            </div>
          </div>
        </div>


        <!-- Upload Signature - Hidden for Premier Uganda -->
        <div v-if="subsidiary !== 'premieruganda'" class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200 shadow-sm">
          <h4 class="text-lg font-semibold mb-3" :style="{ color: theme.primaryColor }">Approval Signature</h4>

          <div class="flex items-center space-x-4">
            <div class="flex-1">
              <p class="text-gray-600 mb-2">Please upload your signature to approve this request:</p>
              <div
                class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-500 transition-colors"
                @click="triggerFileInput"
              >
                <input
                  type="file"
                  ref="signatureInput"
                  accept="image/*"
                  @change="handleSignatureUpload"
                  class="hidden"
                />
                <div v-if="!signatureFile" class="py-6">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p class="mt-2 text-sm text-gray-500">Click to upload signature</p>
                  <p class="text-xs text-gray-400">PNG, JPG, GIF up to 10MB</p>
                </div>
                <div v-else class="py-2">
                  <img
                    :src="signaturePreview"
                    alt="Signature Preview"
                    class="max-h-24 mx-auto object-contain"
                  />
                  <p class="mt-2 text-sm text-gray-500">{{ signatureFile.name }}</p>
                  <button
                    @click.stop="removeSignature"
                    class="mt-1 text-xs text-red-500 hover:text-red-700"
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>

            <div v-if="isProcessing" class="flex-shrink-0 w-32 flex flex-col items-center justify-center">
              <div class="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-2"></div>
              <p class="text-sm text-gray-600">Processing...</p>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="closeApprovalModal"
            class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="approveRequest"
            class="px-6 py-2 rounded-md text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            :style="{ backgroundColor: theme.primaryColor }"
            :disabled="isButtonDisabled"
            :class="{'opacity-50 cursor-not-allowed': isButtonDisabled}"
          >
            <span v-if="!isProcessing">Submit Approval</span>
            <span v-else>Processing...</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Rejection Reason Dialog with transparent backdrop -->
  <div v-if="showRejectionDialog" class="fixed inset-0 flex items-center justify-center bg-transparent backdrop-blur-sm z-50">
    <div class="bg-white/95 rounded-lg shadow-xl p-6 w-full max-w-md">
      <h3 class="text-xl font-semibold mb-2 text-gray-800 flex items-center">
        <span :style="{ color: theme.primaryColor }">Rejection Reason</span>
        <div class="ml-auto">
          <button @click="cancelRejection" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </h3>
      <p class="text-gray-600 mb-4">Please provide a reason for rejecting this request:</p>

      <textarea
        v-model="rejectionReason"
        class="w-full border border-gray-300 rounded-md p-2 mb-4 h-32 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
        placeholder="Enter rejection reason..."
      ></textarea>

      <div class="flex justify-end space-x-3">
        <button
          @click="cancelRejection"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="confirmRejection"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          :disabled="!rejectionReason.trim()"
          :class="{'opacity-50 cursor-not-allowed': !rejectionReason.trim()}"
        >
          Reject Request
        </button>
      </div>
    </div>
  </div>

  <!-- Edit Request Modal -->
  <div v-if="showEditModal" class="fixed inset-0 flex items-center justify-center bg-transparent backdrop-blur-sm z-50">
    <div class="bg-white/95 p-6 rounded-lg shadow-xl max-w-7xl w-full max-h-[95vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6 border-b pb-4">
        <h3 class="text-2xl font-bold text-gray-800">✏️ Edit Request Details</h3>
        <button @click="closeEditModal" class="text-gray-500 hover:text-gray-700 p-2">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- IMPORTANT NOTICE -->
      <div class="mb-4 p-4 bg-green-100 border-l-4 border-green-500 text-green-700">
        <p class="font-semibold">📋 Review Original Details Below Before Making Edits</p>
        <p class="text-sm">The complete original request details are shown below, followed by the edit form.</p>
      </div>

      <!-- ORIGINAL REQUEST DETAILS SECTION - ALWAYS VISIBLE -->
      <div class="mb-8 p-6 bg-blue-50 rounded-lg border-2 border-blue-200 shadow-lg">
        <h4 class="text-xl font-bold mb-6 text-blue-800 flex items-center border-b-2 border-blue-300 pb-3">
          <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          📋 ORIGINAL REQUEST DETAILS (As Submitted by User)
        </h4>

        <!-- Debug info to verify data is loaded -->
        <div class="mb-4 p-3 bg-yellow-100 border border-yellow-400 rounded-lg text-sm">
          <strong>🔍 Debug Info:</strong>
          <span v-if="selectedRequest">
            Request ID: {{ selectedRequest?.id }}, User: {{ selectedRequest?.firstName }} {{ selectedRequest?.lastName }}, Status: {{ selectedRequest?.approvalStatus }}
          </span>
          <span v-else class="text-red-600">selectedRequest is null or undefined</span>
        </div>
        <!-- Personal Information Section -->
        <div class="mb-6">
          <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Personal Information</h5>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">First Name</p>
              <p class="font-medium">{{ selectedRequest?.firstName }}</p>
            </div>
            <div v-if="selectedRequest?.middleName" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Middle Name</p>
              <p class="font-medium">{{ selectedRequest?.middleName }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Last Name</p>
              <p class="font-medium">{{ selectedRequest?.lastName }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Email</p>
              <p class="font-medium">{{ selectedRequest?.email || 'N/A' }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Telephone No</p>
              <p class="font-medium">{{ selectedRequest?.telephone }}</p>
            </div>
            <!-- Premier Uganda specific fields -->
            <div v-if="subsidiary === 'premieruganda' && selectedRequest?.nin" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">NIN</p>
              <p class="font-medium">{{ selectedRequest?.nin }}</p>
            </div>
            <div v-if="subsidiary === 'premieruganda' && selectedRequest?.dateOfBirth" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Date of Birth</p>
              <p class="font-medium">{{ selectedRequest?.dateOfBirth }}</p>
            </div>
            <div v-if="selectedRequest?.tin" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">TIN</p>
              <p class="font-medium">{{ selectedRequest?.tin }}</p>
            </div>
            <div v-if="selectedRequest?.nssf" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">NSSF</p>
              <p class="font-medium">{{ selectedRequest?.nssf }}</p>
            </div>
          </div>
        </div>

        <!-- Next of Kin Information (Premier Uganda) -->
        <div v-if="subsidiary === 'premieruganda' && (selectedRequest?.nextOfKinName || selectedRequest?.nextOfKinMobile)" class="mb-6">
          <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Next of Kin Information</h5>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div v-if="selectedRequest?.nextOfKinName" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Next of Kin Name</p>
              <p class="font-medium">{{ selectedRequest?.nextOfKinName }}</p>
            </div>
            <div v-if="selectedRequest?.nextOfKinMobile" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Next of Kin Mobile</p>
              <p class="font-medium">{{ selectedRequest?.nextOfKinMobile }}</p>
            </div>
            <div v-if="selectedRequest?.nextOfKinRelationship" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Relationship</p>
              <p class="font-medium">{{ selectedRequest?.nextOfKinRelationship }}</p>
            </div>
            <div v-if="selectedRequest?.nextOfKinDependents" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Number of Dependents</p>
              <p class="font-medium">{{ selectedRequest?.nextOfKinDependents }}</p>
            </div>
          </div>
        </div>

        <!-- System Access Information -->
        <div class="mb-6">
          <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">System Access Information</h5>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">System Name</p>
              <p class="font-medium">{{ formatSystemName(selectedRequest?.systemName) }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Branch</p>
              <p class="font-medium">{{ selectedRequest?.branch }}</p>
            </div>
            <div v-if="subsidiary !== 'premieruganda'" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Department</p>
              <p class="font-medium">{{ selectedRequest?.department }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Access Type</p>
              <p class="font-medium">{{ selectedRequest?.accessType }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Role to be Assigned</p>
              <p class="font-medium">{{ selectedRequest?.role }}</p>
            </div>
            <div v-if="selectedRequest?.previousRole" class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Previous Role</p>
              <p class="font-medium">{{ selectedRequest?.previousRole }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100 col-span-2">
              <p class="text-gray-500 mb-1">Reason for Access</p>
              <p class="font-medium">{{ selectedRequest?.reason || 'N/A' }}</p>
            </div>
          </div>
        </div>

        <!-- Request Status Information -->
        <div class="mb-6">
          <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Request Status</h5>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Submitted Date</p>
              <p class="font-medium">{{ formatDate(selectedRequest?.createdAt) }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Current Status</p>
              <p class="font-medium" :class="statusClass(selectedRequest?.approvalStatus)">{{ selectedRequest?.approvalStatus }}</p>
            </div>
            <div v-if="selectedRequest?.rejectionReason" class="bg-white p-3 rounded-md shadow-sm border border-gray-100 col-span-2">
              <p class="text-gray-500 mb-1">Rejection Reason</p>
              <p class="font-medium text-red-600">{{ selectedRequest?.rejectionReason }}</p>
            </div>
          </div>
        </div>

        <!-- Supporting Documents -->
        <div v-if="selectedRequest?.attachments && selectedRequest?.attachments.length > 0" class="mb-6">
          <h5 class="text-md font-semibold mb-3 text-gray-700 border-b pb-1">Supporting Documents</h5>
          <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
            <div class="grid grid-cols-1 gap-2">
              <div v-for="(attachment, index) in selectedRequest?.attachments" :key="index" class="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span class="text-sm font-medium">{{ attachment.filename || `Document ${index + 1}` }}</span>
                </div>
                <span class="text-xs text-gray-500">{{ attachment.contentType || 'Unknown type' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- HR Information for Premier Uganda -->
        <div v-if="subsidiary === 'premieruganda'" class="mt-4">
          <h5 class="text-md font-semibold mb-3 text-blue-800">HR Information</h5>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Staff ID Number</p>
              <p class="font-medium">{{ selectedRequest?.staffId || 'Not filled' }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Position</p>
              <p class="font-medium">{{ selectedRequest?.position || 'Not filled' }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Department</p>
              <p class="font-medium">{{ selectedRequest?.department || 'Not selected' }}</p>
            </div>
            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-gray-500 mb-1">Start Date</p>
              <p class="font-medium">{{ selectedRequest?.startDate || 'Not set' }}</p>
            </div>
          </div>
        </div>
      </div>



      <div class="border-t-4 border-orange-300 pt-8 mb-6 bg-gradient-to-r from-orange-50 to-yellow-50 p-6 rounded-xl">
        <h4 class="text-xl font-bold mb-6 text-orange-900 flex items-center border-b-2 border-orange-200 pb-3">
          <svg class="w-6 h-6 mr-3 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          ✏️ EDIT FORM (Make Changes Below)
        </h4>
        <p class="text-orange-800 mb-4 text-sm bg-orange-100 p-3 rounded-lg border border-orange-200">
          <strong>Instructions:</strong> Review the original details above, then make any necessary changes in the form below. Only edit fields that need to be corrected.
        </p>
      </div>

      <form @submit.prevent="saveEditedRequest" class="space-y-6" @click="closeSystemDropdownOnOutsideClick">

        <!-- Personal Information Section -->
        <div class="bg-gray-50 p-4 rounded-lg border">
          <h5 class="text-lg font-semibold mb-4 text-gray-800">Personal Information</h5>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <input
                type="text"
                v-model="editForm.firstName"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div v-if="subsidiary === 'premieruganda'">
              <label class="block text-sm font-medium text-gray-700 mb-1">Middle Name (Optional)</label>
              <input
                type="text"
                v-model="editForm.middleName"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <input
                type="text"
                v-model="editForm.lastName"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email {{ subsidiary === 'premieruganda' ? '(Optional)' : '' }}</label>
              <input
                type="email"
                v-model="editForm.email"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :required="subsidiary !== 'premieruganda'"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Telephone</label>
              <input
                type="text"
                v-model="editForm.telephone"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <!-- Premier Uganda specific fields -->
            <div v-if="subsidiary === 'premieruganda'">
              <label class="block text-sm font-medium text-gray-700 mb-1">NIN</label>
              <input
                type="text"
                v-model="editForm.nin"
                maxlength="14"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="14 characters"
              />
            </div>
            <div v-if="subsidiary === 'premieruganda'">
              <label class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
              <input
                type="date"
                v-model="editForm.dateOfBirth"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div v-if="subsidiary === 'premieruganda'">
              <label class="block text-sm font-medium text-gray-700 mb-1">TIN</label>
              <input
                type="text"
                v-model="editForm.tin"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div v-if="subsidiary === 'premieruganda'">
              <label class="block text-sm font-medium text-gray-700 mb-1">NSSF Number</label>
              <input
                type="text"
                v-model="editForm.nssf"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <!-- Next of Kin Information (Premier Uganda only) -->
        <div v-if="subsidiary === 'premieruganda'" class="bg-gray-50 p-4 rounded-lg border">
          <h5 class="text-lg font-semibold mb-4 text-gray-800">Next of Kin Information</h5>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Next of Kin Name</label>
              <input
                type="text"
                v-model="editForm.nextOfKinName"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Next of Kin Mobile</label>
              <input
                type="text"
                v-model="editForm.nextOfKinMobile"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Relationship</label>
              <input
                type="text"
                v-model="editForm.nextOfKinRelationship"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Number of Dependents</label>
              <input
                type="text"
                v-model="editForm.nextOfKinDependents"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <!-- System Access Information -->
        <div class="bg-gray-50 p-4 rounded-lg border">
          <h5 class="text-lg font-semibold mb-4 text-gray-800">System Access Information</h5>
          <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">System Name</label>
            <div class="relative border rounded p-2 bg-white system-dropdown-container">
              <div
                class="cursor-pointer"
                @click="showSystemDropdown = !showSystemDropdown"
              >
                <span v-if="editForm.systemName && editForm.systemName.length">
                  {{
                    editForm.systemName.includes("ALL")
                      ? "All Systems"
                      : editForm.systemName.join(", ")
                  }}
                </span>
                <span v-else class="text-gray-400">Select system(s)</span>
              </div>
              <div
                v-if="showSystemDropdown"
                class="absolute z-10 bg-white border rounded mt-1 shadow max-h-48 overflow-y-auto w-full"
              >
                <div class="p-2">
                  <label class="block">
                    <input
                      type="checkbox"
                      value="ALL"
                      v-model="editForm.systemName"
                      @change="toggleAllSystems"
                      class="mr-2"
                    />
                    All
                  </label>
                  <label
                    v-for="system in systemNames"
                    :key="system"
                    class="block mt-1"
                  >
                    <input
                      type="checkbox"
                      :value="system"
                      v-model="editForm.systemName"
                      class="mr-2"
                      :disabled="editForm.systemName.includes('ALL')"
                    />
                    {{ system }}
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Branch</label>
            <select
              v-model="editForm.branch"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Branch</option>
              <option v-for="branch in branches" :key="branch" :value="branch">{{ branch }}</option>
            </select>
          </div>
          <div v-if="subsidiary !== 'premieruganda'">
            <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
            <input
              type="text"
              v-model="editForm.department"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Access Type</label>
            <select
              v-model="editForm.accessType"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Access Type</option>
              <option v-for="accessType in accessTypes" :key="accessType" :value="accessType">{{ accessType }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Role to be Assigned</label>
            <select
              v-model="editForm.role"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select Role</option>
              <option v-for="role in roles" :key="role" :value="role">{{ role }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Previous Role {{ subsidiary === 'premieruganda' ? '(Optional)' : '' }}</label>
            <input
              type="text"
              v-model="editForm.previousRole"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter previous role if applicable"
            />
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">Reason for Access</label>
            <textarea
              v-model="editForm.reason"
              rows="3"
              class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Explain the reason for system access"
            ></textarea>
          </div>
        </div>
        </div>

        <!-- HR Fields for Premier Uganda -->
        <div v-if="subsidiary === 'premieruganda'" class="mt-6 p-4 border rounded-md shadow-sm bg-blue-50">
          <h4 class="font-semibold mb-4 text-lg text-blue-800">HR Information</h4>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Staff ID Number</label>
              <input
                type="text"
                v-model="editForm.staffId"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Position</label>
              <input
                type="text"
                v-model="editForm.position"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
              <select
                v-model="editForm.department"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Department</option>
                <option v-for="dept in departments" :key="dept" :value="dept">{{ dept }}</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
              <input
                type="date"
                v-model="editForm.startDate"
                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            @click="closeEditModal"
            class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-6 py-2 rounded-md text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            :style="{ backgroundColor: theme.primaryColor }"
            :disabled="isProcessing"
            :class="{'opacity-50 cursor-not-allowed': isProcessing}"
          >
            <span v-if="!isProcessing">Save Changes</span>
            <span v-else>Saving...</span>
          </button>
        </div>
      </form>
    </div>
  </div>


</template>

<script>
import {
  getAllAccessRequests,
  getAccessRequestById,
  approveHR,
  approveIT,
  rejectAccessRequest,
  getDepartments,
  updateHRFields,
  updateRequestDetails,
  getBranches,
  getRoles,
} from "@/services/apiService";
import { subsidiaries } from "@/config/subsidiaries";
import { formatDateForDisplay } from "@/utils/dateUtils";

export default {
  props: ["subsidiary"],
  computed: {
    theme() {
      return subsidiaries[this.subsidiary];
    },
    filteredRequests() {
      return this.requests.filter(
        (request) =>
          request.firstName.toLowerCase().includes(this.search.toLowerCase()) ||
          request.lastName.toLowerCase().includes(this.search.toLowerCase()) ||
          (this.subsidiary !== 'premieruganda' && request.department?.toLowerCase().includes(this.search.toLowerCase()))
      );
    },
    totalPages() {
      return Math.ceil(this.filteredRequests.length / this.itemsPerPage);
    },
    paginatedRequests() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      return this.filteredRequests.slice(start, start + this.itemsPerPage);
    },
    paginationOptions() {
      return Array.from(
        { length: Math.ceil(this.requests.length / 10) },
        (_, i) => (i + 1) * 10
      );
    },
    isButtonDisabled() {
      // If processing, always disable
      if (this.isProcessing) {
        return true;
      }

      // For Premier Uganda, signature is not required, so only check processing state
      if (this.subsidiary === 'premieruganda') {
        return false;
      }

      // For all other subsidiaries, require signature file
      return !this.signatureFile;
    },
  },
  data() {
    return {
      requests: [],
      showApprovalModal: false,
      showRejectionDialog: false,
      rejectionReason: "",
      requestToReject: null,
      selectedRequest: null,
      search: "",
      itemsPerPage: 10,
      currentPage: 1,
      approver: { name: "", department: "", signature: "" },
      signatureFile: null,
      signaturePreview: null,
      isProcessing: false,
      departments: [],
      branches: [],
      roles: [],
      systemNames: [],
      accessTypes: [
        "New Access",
        "Additional Access",
        "Modify Existing Access",
        "Disable Access (Access no longer required)",
        "Intranet Access",
      ],
      hrFields: {
        staffId: '',
        position: '',
        department: '',
        startDate: ''
      },
      showEditModal: false,
      showSystemDropdown: false,
      editForm: {
        firstName: '',
        middleName: '',
        lastName: '',
        email: '',
        telephone: '',
        nin: '',
        dateOfBirth: '',
        tin: '',
        nssf: '',
        nextOfKinName: '',
        nextOfKinMobile: '',
        nextOfKinRelationship: '',
        nextOfKinDependents: '',
        systemName: [],
        branch: '',
        department: '',
        accessType: '',
        role: '',
        previousRole: '',
        reason: '',
        staffId: '',
        position: '',
        startDate: ''
      },
    };
  },
  async created() {
    console.log("Component created with subsidiary:", this.subsidiary);
    await this.fetchRequests();
    if (this.subsidiary === 'premieruganda') {
      await this.fetchDepartments();
    }
    // Fetch dropdown data for edit functionality
    await this.fetchBranches();
    await this.fetchRoles();
    this.setSystemNamesBySubsidiary();
  },
  methods: {
    async fetchRequests() {
      try {
        console.log("Fetching requests for subsidiary:", this.subsidiary);
        const data = await getAllAccessRequests(this.subsidiary);
        console.log("Received requests data:", data);
        this.requests = data || [];
      } catch (error) {
        console.error("Error fetching requests:", error);
        console.error("Error response:", error.response);
        console.error("Error status:", error.response?.status);

        // Check if it's an authentication error
        if (error.response && error.response.status === 401) {
          console.log("Authentication error detected, redirecting to login");
          // Clear stored authentication data
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          // Redirect to login page
          this.$router.push(`/login/${this.subsidiary}`);
          return;
        }

        // Show error notification for other errors
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Error loading requests. Please try again.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);
      }
    },
    async fetchDepartments() {
      try {
        const response = await getDepartments(this.subsidiary);
        this.departments = response.map((d) => d.value);
      } catch (error) {
        console.error('Error fetching departments:', error);
        this.departments = [];
      }
    },
    async fetchBranches() {
      try {
        console.log('Fetching branches for subsidiary:', this.subsidiary);
        const response = await getBranches(this.subsidiary);
        this.branches = response.map((b) => b.value);
        console.log('Branches fetched:', this.branches);
      } catch (error) {
        console.error('Error fetching branches:', error);
        this.branches = [];
      }
    },
    async fetchRoles() {
      try {
        console.log('Fetching roles for subsidiary:', this.subsidiary);
        const response = await getRoles(this.subsidiary);
        this.roles = response.map((r) => r.value);
        console.log('Roles fetched:', this.roles);
      } catch (error) {
        console.error('Error fetching roles:', error);
        this.roles = [];
      }
    },
    setSystemNamesBySubsidiary() {
      // Default system names for most subsidiaries
      const defaultSystemNames = ["Mambu", "Email", "Akili", "Juakali", "Yeastar"];

      // Premier Uganda specific system names
      const premierUgandaSystemNames = [
        "Mambu",
        "CRM",
        "Yeastar",
        "Smart Collect",
        "Juakali",
        "PCA / HCM",
        "Pepea",
        "D365",
        "PLA"
      ];

      // Set system names based on subsidiary
      switch (this.subsidiary) {
        case 'premieruganda':
          this.systemNames = premierUgandaSystemNames;
          break;
        default:
          this.systemNames = defaultSystemNames;
          break;
      }
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    },
    statusClass(status) {
      return {
        "text-green-600": status.includes("Approved"),
        "text-red-600": status.includes("Rejected"),
        "text-yellow-500": status === "Pending",
        "text-orange-500": status === "Returned for Editing",
      };
    },
    canApprove(status) {
      // For premierkenya, allow approval for Pending, Approved by Branch Head, and Approved by HR
      if (this.subsidiary === 'premierkenya') {
        return ["Pending", "Approved by Branch Head", "Approved by HR"].includes(status);
      }
      // For premieruganda, only allow approval for Pending (HR approval is final)
      if (this.subsidiary === 'premieruganda') {
        return ["Pending"].includes(status);
      }
      // For other subsidiaries, allow approval for Pending and Approved by HR
      return ["Pending", "Approved by HR"].includes(status);
    },
    canReject(status) {
      // Allow rejection for requests that are pending or in approval process
      return ["Pending", "Approved by Branch Head", "Approved by HR"].includes(status);
    },
    canEdit(status) {
      // HR can edit requests that are pending or returned for editing
      return ["Pending", "Returned for Editing"].includes(status);
    },
    async openApprovalModal(request) {
      try {
        console.log('Opening approval modal for request ID:', request.id);
        console.log('Current request data from list:', request);

        // First refresh the entire requests list to get latest data
        console.log('Refreshing requests list...');
        await this.fetchRequests();

        // Find the updated request from the refreshed list
        const updatedRequest = this.requests.find(r => r.id === request.id);
        console.log('Updated request from refreshed list:', updatedRequest);

        if (updatedRequest) {
          this.selectedRequest = updatedRequest;
          console.log('Using updated request data');
          console.log('Updated NIN:', updatedRequest.nin);
          console.log('Updated telephone:', updatedRequest.telephone);
        } else {
          console.log('Request not found in refreshed list, using original');
          this.selectedRequest = request;
        }

        this.showApprovalModal = true;
        this.signatureFile = null;

        // Reset HR fields for Premier Uganda using updated data
        if (this.subsidiary === 'premieruganda') {
          this.hrFields = {
            staffId: this.selectedRequest.staffId || '',
            position: this.selectedRequest.position || '',
            department: this.selectedRequest.department || '',
            startDate: this.selectedRequest.startDate || ''
          };
        }
      } catch (error) {
        console.error('Error refreshing request data:', error);
        // Fallback to using the request from the list
        this.selectedRequest = request;
        this.showApprovalModal = true;
        this.signatureFile = null;

        // Reset HR fields for Premier Uganda
        if (this.subsidiary === 'premieruganda') {
          this.hrFields = {
            staffId: request.staffId || '',
            position: request.position || '',
            department: request.department || '',
            startDate: request.startDate || ''
          };
        }
      }
    },
    closeApprovalModal() {
      this.showApprovalModal = false;
    },
    triggerFileInput() {
      this.$refs.signatureInput.click();
    },

    handleSignatureUpload(event) {
      const file = event.target.files[0];
      if (file && file.type.startsWith("image/")) {
        this.signatureFile = file;
        // Create preview URL
        this.signaturePreview = URL.createObjectURL(file);
      } else {
        alert("Please upload a valid image file.");
      }
    },

    removeSignature() {
      this.signatureFile = null;
      this.signaturePreview = null;
    },

    getStepClass(step) {
      const status = this.selectedRequest?.approvalStatus;

      if (this.subsidiary === 'premierkenya') {
        // Premier Kenya workflow: Branch Head -> HR -> IT
        if (step === 1) { // Branch Head Approval
          if (status === 'Pending') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('Branch Head') || status.includes('HR') || status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          }
        } else if (step === 2) { // HR Approval
          if (status === 'Approved by Branch Head') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('HR') || status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          } else {
            return 'bg-gray-300 text-gray-600'; // Future step
          }
        } else if (step === 3) { // IT Approval
          if (status === 'Approved by HR') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          } else {
            return 'bg-gray-300 text-gray-600'; // Future step
          }
        }
      } else {
        // Other subsidiaries workflow: HR -> IT
        if (step === 1) { // HR Approval
          if (status === 'Pending') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('HR') || status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          }
        } else if (step === 2) { // IT Approval
          if (status === 'Approved by HR') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          } else {
            return 'bg-gray-300 text-gray-600'; // Future step
          }
        }
      }

      return 'bg-gray-300 text-gray-600'; // Default
    },

    getProgressColor(step, isDirectToIT = false) {
      const status = this.selectedRequest?.approvalStatus;

      if (this.subsidiary === 'premierkenya') {
        // Premier Kenya workflow: Submitted -> Branch Head -> HR -> IT
        if (step === 1) { // Progress between Submitted and Branch Head
          return status === 'Pending' ? '#EAB308' : '#22C55E';
        } else if (step === 2) { // Progress between Branch Head and HR
          if (status === 'Pending') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by Branch Head') {
            return '#EAB308'; // Yellow for current
          } else if (status.includes('HR') || status.includes('IT')) {
            return '#22C55E'; // Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        } else if (step === 3) { // Progress between HR and IT
          if (status === 'Pending' || status === 'Approved by Branch Head') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by HR') {
            return '#EAB308'; // Yellow for current
          } else if (status.includes('IT')) {
            return '#22C55E'; // Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        }
      } else {
        // Other subsidiaries workflow: Submitted -> HR -> IT
        // For the direct HR to IT path
        if (isDirectToIT) {
          if (status === 'Pending') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by HR' || status.includes('IT')) {
            return status === 'Approved by HR' ? '#EAB308' : '#22C55E'; // Yellow for current, Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        }

        // Standard path
        if (step === 1) { // Progress between Submitted and HR
          return status === 'Pending' ? '#EAB308' : '#22C55E';
        } else if (step === 2) { // Progress between HR and IT
          if (status === 'Pending') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by HR' || status.includes('IT')) {
            return status === 'Approved by HR' ? '#EAB308' : '#22C55E'; // Yellow for current, Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        }
      }

      return '#D1D5DB'; // Default gray
    },
    getBranchHeadTitle() {
      // Determine branch head title based on the selected request's branch
      const branch = this.selectedRequest?.branch?.toLowerCase() || '';

      if (branch.startsWith('govt') || branch.includes('call center')) {
        return 'Head of Checkoff';
      } else if (branch.includes('head office') || branch.includes('karen')) {
        return 'Head of Finance';
      } else {
        return 'Head of SME';
      }
    },
    formatDate(date) {
      // Format date with correct timezone for the subsidiary
      return formatDateForDisplay(date, this.subsidiary);
    },
    async approveBranchHead(requestId, subsidiary) {
      try {
        const response = await fetch(`http://localhost:7081/user-access/v1/access-request/${requestId}/approve-branch-head`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-subsidiary': subsidiary,
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to approve request');
        }

        return await response.json();
      } catch (error) {
        console.error('Error approving branch head:', error);
        throw error;
      }
    },
    async convertToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
    },
    async approveHRWithFields(requestId, subsidiary, hrFields) {
      try {
        console.log('=== approveHRWithFields called ===');
        console.log('Request ID:', requestId);
        console.log('Subsidiary:', subsidiary);
        console.log('HR Fields:', hrFields);
        console.log('Current component subsidiary:', this.subsidiary);

        // Validate that this is only called for Premier Uganda
        if (subsidiary !== 'premieruganda') {
          throw new Error(`approveHRWithFields should only be called for Premier Uganda, but was called for: ${subsidiary}`);
        }

        // First update the request with HR fields using the API service
        console.log('Calling updateHRFields API...');
        await updateHRFields(requestId, hrFields, subsidiary);

        console.log('HR fields updated successfully, proceeding with HR approval');

        // Then proceed with normal HR approval
        await approveHR(requestId, subsidiary);

        console.log('HR approval completed successfully');
      } catch (error) {
        console.error('Error in approveHRWithFields:', error);
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);
        throw error;
      }
    },
    async approveRequest() {
      if (!this.selectedRequest) return;

      try {
        // Validate HR fields for Premier Uganda
        if (this.subsidiary === 'premieruganda') {
          const missingFields = [];
          if (!this.hrFields.staffId?.trim()) missingFields.push('Staff ID Number');
          if (!this.hrFields.position?.trim()) missingFields.push('Position');
          if (!this.hrFields.department?.trim()) missingFields.push('Department');
          if (!this.hrFields.startDate?.trim()) missingFields.push('Start Date');

          if (missingFields.length > 0) {
            const notificationEl = document.createElement('div');
            notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
            notificationEl.innerHTML = `
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span>Please fill in all HR fields: ${missingFields.join(', ')}</span>
              </div>
            `;
            document.body.appendChild(notificationEl);
            setTimeout(() => {
              notificationEl.remove();
            }, 5000);
            return;
          }
        }

        // Signature is not required for Premier Uganda
        if (this.subsidiary !== 'premieruganda' && !this.signatureFile) {
          // Using a more user-friendly notification instead of alert
          const notificationEl = document.createElement('div');
          notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
          notificationEl.innerHTML = `
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              <span>Please upload a signature before approving.</span>
            </div>
          `;
          document.body.appendChild(notificationEl);
          setTimeout(() => {
            notificationEl.remove();
          }, 3000);
          return;
        }

        // Set processing state
        this.isProcessing = true;

        // Convert signature to base64 (only if signature file exists)
        if (this.signatureFile) {
          const base64Signature = await this.convertToBase64(this.signatureFile);
          this.approver.signature = base64Signature;
        } else {
          // For Premier Uganda, no signature is required
          this.approver.signature = null;
        }

        console.log("Approving request for ID:", this.selectedRequest.id);

        // Determine which approval endpoint to call based on current status and subsidiary
        try {
          if (this.subsidiary === 'premierkenya') {
            // Premier Kenya workflow: Branch Head -> HR -> IT
            if (this.selectedRequest.approvalStatus === "Pending") {
              console.log(`Calling approveBranchHead for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await this.approveBranchHead(this.selectedRequest.id, this.subsidiary);
            } else if (this.selectedRequest.approvalStatus === "Approved by Branch Head") {
              console.log(`Calling approveHR for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await approveHR(this.selectedRequest.id, this.subsidiary);
            } else if (this.selectedRequest.approvalStatus === "Approved by HR") {
              console.log(`Calling approveIT for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await approveIT(this.selectedRequest.id, this.subsidiary);
            } else {
              throw new Error(`Invalid approval status for premierkenya: ${this.selectedRequest.approvalStatus}`);
            }
          } else {
            // Other subsidiaries workflow: HR -> IT
            if (this.selectedRequest.approvalStatus === "Pending") {
              console.log(`=== HR Approval Process ===`);
              console.log(`Request ID: ${this.selectedRequest.id}`);
              console.log(`Subsidiary: ${this.subsidiary}`);
              console.log(`Request Status: ${this.selectedRequest.approvalStatus}`);

              // For Premier Uganda, include HR fields in the approval
              if (this.subsidiary === 'premieruganda') {
                console.log('Taking Premier Uganda path - calling approveHRWithFields');
                await this.approveHRWithFields(this.selectedRequest.id, this.subsidiary, this.hrFields);
              } else {
                console.log('Taking standard path - calling approveHR');
                await approveHR(this.selectedRequest.id, this.subsidiary);
              }
            } else if (this.selectedRequest.approvalStatus.includes("HR")) {
              console.log(`Calling approveIT directly after HR for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await approveIT(this.selectedRequest.id, this.subsidiary);
            } else {
              throw new Error(`Invalid approval status: ${this.selectedRequest.approvalStatus}`);
            }
          }
        } catch (approvalError) {
          console.error("Approval API error:", approvalError);
          throw approvalError;
        }

        // Show success notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>Request approved successfully!</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);

        // Close modal and refresh data
        this.closeApprovalModal();
        this.fetchRequests();
      } catch (error) {
        console.error("Error approving request:", error);

        // Extract error message from the response if available
        let errorMessage = "Error approving request. Please try again.";

        if (error.response && error.response.data) {
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.data.error) {
            errorMessage = error.response.data.error;
          }
        }

        console.log("Error details:", error.response?.data || error.message);

        // Show error notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>${errorMessage}</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 5000);
      } finally {
        // Reset processing state
        this.isProcessing = false;
      }
    },
    // Open rejection dialog
    rejectRequest(requestId) {
      this.requestToReject = requestId;
      this.rejectionReason = "";
      this.showRejectionDialog = true;
    },
    openRejectionModal(request) {
      this.requestToReject = request.id;
      this.rejectionReason = "";
      this.showRejectionDialog = true;
    },

    // Cancel rejection
    cancelRejection() {
      this.showRejectionDialog = false;
      this.requestToReject = null;
      this.rejectionReason = "";
    },

    // Confirm rejection with reason
    async confirmRejection() {
      if (!this.rejectionReason.trim()) {
        return;
      }

      try {
        await rejectAccessRequest(this.requestToReject, this.subsidiary, this.rejectionReason);

        // Show success notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Request rejected successfully.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);

        // Close the dialog and refresh the requests list
        this.showRejectionDialog = false;
        this.requestToReject = null;
        this.rejectionReason = "";
        this.fetchRequests();
      } catch (error) {
        console.error("Error rejecting request:", error);

        // Show error notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Error rejecting request. Please try again.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);
      }
    },
    toggleAllSystems() {
      if (this.editForm.systemName.includes("ALL")) {
        this.editForm.systemName = ["ALL"];
      } else {
        this.editForm.systemName = [];
      }
    },
    closeSystemDropdownOnOutsideClick(event) {
      // Close dropdown if clicking outside the system dropdown area
      if (!event.target.closest('.system-dropdown-container')) {
        this.showSystemDropdown = false;
      }
    },
    formatSystemName(systemName) {
      if (!systemName) return 'N/A';

      if (Array.isArray(systemName)) {
        if (systemName.includes('ALL')) {
          return 'All Systems';
        }
        return systemName.join(', ');
      }

      if (typeof systemName === 'string') {
        try {
          const parsed = JSON.parse(systemName);
          if (Array.isArray(parsed)) {
            if (parsed.includes('ALL')) {
              return 'All Systems';
            }
            return parsed.join(', ');
          }
        } catch (e) {
          // If parsing fails, return as is
          return systemName;
        }
      }

      return systemName;
    },
    // Edit functionality methods
    async openEditModal(request) {
      try {
        console.log('Opening edit modal with request:', request);

        // Fetch the latest request data from server
        const response = await getAccessRequestById(request.id, this.subsidiary);
        const latestRequest = response.data;

        this.selectedRequest = latestRequest;
        this.showEditModal = true;
        this.showSystemDropdown = false;
        console.log('selectedRequest set to latest data:', this.selectedRequest);

        this.populateEditForm(latestRequest);
      } catch (error) {
        console.error('Error fetching latest request data for edit:', error);
        // Fallback to using the request from the list
        this.selectedRequest = request;
        this.showEditModal = true;
        this.showSystemDropdown = false;
        console.log('selectedRequest set to fallback data:', this.selectedRequest);

        this.populateEditForm(request);
      }
    },

    populateEditForm(request) {

      // Populate edit form with current request data
      // Handle systemName - convert string to array if needed
      let systemNameArray = [];
      if (request.systemName) {
        if (Array.isArray(request.systemName)) {
          systemNameArray = request.systemName;
        } else if (typeof request.systemName === 'string') {
          // If it's a string, try to parse it or split it
          try {
            systemNameArray = JSON.parse(request.systemName);
          } catch (e) {
            // If parsing fails, split by comma or treat as single item
            systemNameArray = request.systemName.includes(',')
              ? request.systemName.split(',').map(s => s.trim())
              : [request.systemName];
          }
        }
      }

      // Handle accessType - it's stored as an array in the database
      let accessTypeValue = '';
      if (request.accessType) {
        if (Array.isArray(request.accessType)) {
          // If it's an array, take the first value for the dropdown
          accessTypeValue = request.accessType[0] || '';
        } else {
          accessTypeValue = request.accessType;
        }
      }

      // Populate the edit form with current request data
      const formData = {
        firstName: request.firstName || '',
        middleName: request.middleName || '',
        lastName: request.lastName || '',
        email: request.email || '',
        telephone: request.telephone || '',
        nin: request.nin || '',
        dateOfBirth: request.dateOfBirth || '',
        tin: request.tin || '',
        nssf: request.nssf || '',
        nextOfKinName: request.nextOfKinName || '',
        nextOfKinMobile: request.nextOfKinMobile || '',
        nextOfKinRelationship: request.nextOfKinRelationship || '',
        nextOfKinDependents: request.nextOfKinDependents || '',
        systemName: systemNameArray,
        branch: request.branch || '',
        department: request.department || '',
        accessType: accessTypeValue,
        role: request.role || '',
        previousRole: request.previousRole || '',
        reason: request.reason || '',
        staffId: request.staffId || '',
        position: request.position || '',
        startDate: request.startDate || ''
      };

      // Set the form data using Object.assign to ensure reactivity
      Object.assign(this.editForm, formData);


    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedRequest = null;
      this.showSystemDropdown = false;
      this.editForm = {
        firstName: '',
        middleName: '',
        lastName: '',
        email: '',
        telephone: '',
        nin: '',
        dateOfBirth: '',
        tin: '',
        nssf: '',
        nextOfKinName: '',
        nextOfKinMobile: '',
        nextOfKinRelationship: '',
        nextOfKinDependents: '',
        systemName: [],
        branch: '',
        department: '',
        accessType: '',
        role: '',
        previousRole: '',
        reason: '',
        staffId: '',
        position: '',
        startDate: ''
      };
    },
    async saveEditedRequest() {
      if (!this.selectedRequest) return;

      try {
        this.isProcessing = true;

        // Prepare the form data for submission
        const formDataToSubmit = { ...this.editForm };

        // Convert accessType back to array format for the API
        if (formDataToSubmit.accessType && typeof formDataToSubmit.accessType === 'string') {
          formDataToSubmit.accessType = [formDataToSubmit.accessType];
        }

        // Convert systemName back to string format for the API (it's stored as string, not array)
        if (formDataToSubmit.systemName && Array.isArray(formDataToSubmit.systemName)) {
          formDataToSubmit.systemName = formDataToSubmit.systemName.join(', ');
        }

        // Handle invalid dates - set to null instead of 'Invalid date'
        if (formDataToSubmit.dateOfBirth === 'Invalid date' || formDataToSubmit.dateOfBirth === '') {
          formDataToSubmit.dateOfBirth = null;
        }
        if (formDataToSubmit.startDate === 'Invalid date' || formDataToSubmit.startDate === '') {
          formDataToSubmit.startDate = null;
        }

        // Handle empty strings for optional fields - set to null
        const optionalFields = ['email', 'middleName', 'nin', 'tin', 'nssf', 'previousRole', 'reason', 'staffId', 'position'];
        optionalFields.forEach(field => {
          if (formDataToSubmit[field] === '') {
            formDataToSubmit[field] = null;
          }
        });



        console.log('Saving edited request with data:', formDataToSubmit);
        console.log('NIN being sent:', formDataToSubmit.nin);
        console.log('Telephone being sent:', formDataToSubmit.telephone);
        await updateRequestDetails(this.selectedRequest.id, formDataToSubmit, this.subsidiary);
        console.log('Request updated successfully');

        // Show success notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>Request updated successfully!</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);

        this.closeEditModal();
        await this.fetchRequests(); // Refresh the list

      } catch (error) {
        console.error('Error updating request:', error);

        // Show error notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Error updating request. Please try again.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);
      } finally {
        this.isProcessing = false;
      }
    },
  },
};
</script>
