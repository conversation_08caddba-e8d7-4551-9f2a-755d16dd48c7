{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS='--max-old-space-size=4096' NODE_ENV=prod VITE_APP_STAGE=prod vite build", "buildess": "cross-env NODE_OPTIONS='--max-old-space-size=4096' NODE_ENV=prod VITE_APP_STAGE=prod vite build --dest ../public/ess", "build:dev": "cross-env NODE_OPTIONS='--max-old-space-size=4096' NODE_ENV=develop VITE_APP_STAGE=develop vite build", "dev": "cross-env NODE_OPTIONS='--max-old-space-size=4096' BROWSER=none VITE_APP_STAGE=develop NODE_ENV=develop vite", "preview": "cross-env NODE_OPTIONS='--max-old-space-size=4096' vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "@vueuse/core": "^13.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.503.0", "papaparse": "^5.5.2", "pinia": "^3.0.2", "reka-ui": "^2.2.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "gitHooks": {"pre-commit": "lint-staged"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/google.maps": "^3.58.1", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.2", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss": "^8.5.1", "prettier": "^3.0.0", "sass": "^1.83.4", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.2.8", "typescript": "~5.7.3", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^28.0.0", "unplugin-vue-router": "^0.10.0", "vite": "^6.3.1", "vite-plugin-vue-devtools": "^7.7.0", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.0.3", "vue-tsc": "^2.2.0"}}